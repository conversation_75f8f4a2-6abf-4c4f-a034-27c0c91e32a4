import asyncio
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional
from elasticsearch import AsyncElasticsearch, helpers
from elasticsearch.exceptions import  ConnectionError
from src.domain.interfaces import RepositoryInterface
from src.domain.entities import MainCategory, SubCategory
from src.config import Settings
from src.infrastructure.exceptions import ElasticsearchBulkError, ElasticsearchConnectionError

# 設定日誌
logger = logging.getLogger(__name__)


class ElasticsearchRepository(RepositoryInterface):
    """Elasticsearch資料儲存庫實作"""

    def __init__(self, settings: Settings):
        self.settings = settings

        # 根據環境設定 CA 證書路徑
        ca_cert_path = None
        if settings.environment in ["local", "dev", "prod"]:
            # 使用專案根目錄來建構證書路徑
            ca_cert_path = settings.project_root / "certs" / "es" / settings.environment / "ca" / "ca.crt"
            print(f"settings.project_root:{settings.project_root}")
            print(f"ca_cert_path:{ca_cert_path}")

            # 確保證書檔案存在
            if not ca_cert_path.exists():
                ca_cert_path = None

        # 建立 Elasticsearch 客戶端配置
        client_config = {
            "hosts": [settings.elasticsearch_url],
        }

        # 加入基本驗證（如果有設定）
        if settings.elasticsearch_username:
            client_config["basic_auth"] = (
                settings.elasticsearch_username,
                settings.elasticsearch_password
            )

        # 加入 CA 證書（如果存在）
        if ca_cert_path:
            # client_config["ca_certs"] = ca_cert_path.as_posix()
            client_config["verify_certs"] = False

        print(f"client_config:{client_config}")
        self.client = AsyncElasticsearch(**client_config)

    async def create_indices(self) -> None:
        """創建Elasticsearch索引"""
        # 主案類索引映射
        main_categories_mapping = {
            "mappings": {
                "properties": {
                    "main_category": {"type": "keyword"},
                    "description": {
                        "type": "text",
                        "analyzer": "ik_max_word"
                    },
                    "combined_text": {
                        "type": "text",
                        "analyzer": "ik_max_word"
                    },
                    "sub_categories": {"type": "keyword"},
                    "embedding": {
                        "type": "dense_vector",
                        "dims": self.settings.embedding_dims,
                        "index": True,
                        "similarity": "cosine"
                    },
                    "created_at": {"type": "date", "format": "yyyy/MM/dd HH:mm:ss||yyyy/MM/dd||epoch_millis",}
                }
            },
            "settings": {
                "index": {
                    "number_of_shards": 1,
                    "number_of_replicas": 0
                }
            }
        }

        # 子案類索引映射
        sub_categories_mapping = {
            "mappings": {
                "properties": {
                    "main_category": {"type": "keyword"},
                    "sub_category": {"type": "keyword"},
                    "sub_description": {
                        "type": "text",
                        "analyzer": "ik_max_word"
                    },
                    "main_description": {
                        "type": "text",
                        "analyzer": "ik_max_word"
                    },
                    "combined_text": {
                        "type": "text",
                        "analyzer": "ik_max_word"
                    },
                    "keywords": {"type": "keyword"},
                    "embedding": {
                        "type": "dense_vector",
                        "dims": self.settings.embedding_dims,
                        "index": True,
                        "similarity": "cosine"
                    },
                    "created_at": {"type": "date", "format": "yyyy/MM/dd HH:mm:ss||yyyy/MM/dd||epoch_millis",}
                }
            },
            "settings": {
                "index": {
                    "number_of_shards": 1,
                    "number_of_replicas": 0
                }
            }
        }

        # 創建索引
        if not await self.client.indices.exists(index=self.settings.main_categories_index):
            await self.client.indices.create(
                index=self.settings.main_categories_index,
                body=main_categories_mapping
            )

        if not await self.client.indices.exists(index=self.settings.sub_categories_index):
            await self.client.indices.create(
                index=self.settings.sub_categories_index,
                body=sub_categories_mapping
            )

    async def save_main_category(self, main_category: MainCategory) -> None:
        """儲存主案類"""
        doc = main_category.model_dump()
        await self.client.index(
            index=self.settings.main_categories_index,
            body=doc,
            id=main_category.main_category
        )

    async def save_sub_category(self, sub_category: SubCategory) -> None:
        """儲存子案類"""
        doc = sub_category.model_dump()
        await self.client.index(
            index=self.settings.sub_categories_index,
            body=doc,
            id=f"{sub_category.main_category}_{sub_category.sub_category}"
        )

    async def save_main_categories_batch(self, main_categories: List[MainCategory]) -> None:
        """批次儲存主案類"""
        if not main_categories:
            logger.warning("沒有主案類需要儲存")
            return

        actions = []
        for category in main_categories:
            action = {
                "_index": self.settings.main_categories_index,
                "_id": category.main_category,
                "_source": category.model_dump()
            }
            actions.append(action)

        if actions:
            try:
                # 記錄操作開始
                logger.info(f"開始批次索引 {len(actions)} 筆主案類到索引 {self.settings.main_categories_index}")

                # 執行批次索引
                response = await helpers.async_bulk(
                    self.client,
                    actions,
                    raise_on_error=False,  # 不要在第一個錯誤時拋出異常
                    stats_only=False       # 返回詳細結果
                )

                # 分析結果
                success_count, errors = response

                if errors:
                    # 收集失敗的項目
                    failed_items = []
                    for error in errors:
                        if isinstance(error, dict) and any(key in error for key in ['index', 'create', 'update', 'delete']):
                            operation = next(key for key in ['index', 'create', 'update', 'delete'] if key in error)
                            if error[operation].get('error'):
                                failed_items.append(error)

                    if failed_items:
                        logger.error(f"批次索引主案類時發生錯誤: {len(failed_items)}/{len(actions)} 筆失敗")

                        # 拋出自定義異常
                        raise ElasticsearchBulkError(
                            failed_items=failed_items,
                            total_documents=len(actions),
                            index_name=self.settings.main_categories_index,
                            operation_details={
                                "operation": "save_main_categories_batch",
                                "document_type": "MainCategory"
                            }
                        )

                logger.info(f"成功批次索引 {success_count} 筆主案類")

            except helpers.BulkIndexError as e:
                # 處理 BulkIndexError
                logger.error(f"批次索引主案類時發生 BulkIndexError: {str(e)}")

                # 從 BulkIndexError 提取失敗項目
                failed_items = []
                if hasattr(e, 'errors') and e.errors:
                    failed_items = e.errors

                raise ElasticsearchBulkError(
                    failed_items=failed_items,
                    total_documents=len(actions),
                    index_name=self.settings.main_categories_index,
                    operation_details={
                        "operation": "save_main_categories_batch",
                        "document_type": "MainCategory",
                        "original_error": str(e)
                    }
                )

            except ConnectionError as e:
                logger.error(f"連線錯誤: {str(e)}")
                raise ElasticsearchConnectionError(
                    host=self.settings.elasticsearch_url,
                    original_error=e
                )

            except Exception as e:
                logger.error(f"批次索引主案類時發生未預期的錯誤: {type(e).__name__}: {str(e)}")
                raise

    async def save_sub_categories_batch(self, sub_categories: List[SubCategory]) -> None:
        """批次儲存子案類"""
        if not sub_categories:
            logger.warning("沒有子案類需要儲存")
            return

        actions = []
        for category in sub_categories:
            action = {
                "_index": self.settings.sub_categories_index,
                "_id": f"{category.main_category}_{category.sub_category}",
                "_source": category.model_dump()
            }
            actions.append(action)

        if actions:
            try:
                # 記錄操作開始
                logger.info(f"開始批次索引 {len(actions)} 筆子案類到索引 {self.settings.sub_categories_index}")

                # 執行批次索引
                response = await helpers.async_bulk(
                    self.client,
                    actions,
                    raise_on_error=False,  # 不要在第一個錯誤時拋出異常
                    stats_only=False       # 返回詳細結果
                )

                # 分析結果
                success_count, errors = response

                if errors:
                    # 收集失敗的項目
                    failed_items = []
                    for error in errors:
                        if isinstance(error, dict) and any(key in error for key in ['index', 'create', 'update', 'delete']):
                            operation = next(key for key in ['index', 'create', 'update', 'delete'] if key in error)
                            if error[operation].get('error'):
                                failed_items.append(error)

                    if failed_items:
                        logger.error(f"批次索引子案類時發生錯誤: {len(failed_items)}/{len(actions)} 筆失敗")

                        # 拋出自定義異常
                        raise ElasticsearchBulkError(
                            failed_items=failed_items,
                            total_documents=len(actions),
                            index_name=self.settings.sub_categories_index,
                            operation_details={
                                "operation": "save_sub_categories_batch",
                                "document_type": "SubCategory"
                            }
                        )

                logger.info(f"成功批次索引 {success_count} 筆子案類")

            except helpers.BulkIndexError as e:
                # 處理 BulkIndexError
                logger.error(f"批次索引子案類時發生 BulkIndexError: {str(e)}")

                # 從 BulkIndexError 提取失敗項目
                failed_items = []
                if hasattr(e, 'errors') and e.errors:
                    failed_items = e.errors

                raise ElasticsearchBulkError(
                    failed_items=failed_items,
                    total_documents=len(actions),
                    index_name=self.settings.sub_categories_index,
                    operation_details={
                        "operation": "save_sub_categories_batch",
                        "document_type": "SubCategory",
                        "original_error": str(e)
                    }
                )

            except ConnectionError as e:
                logger.error(f"連線錯誤: {str(e)}")
                raise ElasticsearchConnectionError(
                    host=self.settings.elasticsearch_url,
                    original_error=e
                )

            except Exception as e:
                logger.error(f"批次索引子案類時發生未預期的錯誤: {type(e).__name__}: {str(e)}")
                raise

    async def delete_indices(self) -> None:
        """刪除索引（用於重新建立）"""
        if await self.client.indices.exists(index=self.settings.main_categories_index):
            await self.client.indices.delete(index=self.settings.main_categories_index)

        if await self.client.indices.exists(index=self.settings.sub_categories_index):
            await self.client.indices.delete(index=self.settings.sub_categories_index)

    async def close(self) -> None:
        """關閉連線"""
        await self.client.close()
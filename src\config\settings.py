from pydantic_settings import BaseSettings
from pydantic import Field
from typing import Optional, Dict, Any
from pathlib import Path


class Settings(BaseSettings):
    """應用程式設定"""

    # 專案根目錄（基於當前工作目錄）
    project_root: Path = Field(default_factory=lambda: Path.cwd())

    # 環境設定
    environment: str = Field("local", env="ENVIRONMENT")

    # Gemini API設定
    gemini_api_key: str = Field(..., env="GEMINI_API_KEY")

    # Elasticsearch設定
    elasticsearch_url: str = Field("http://127.0.0.1:9200", env="ELASTICSEARCH_URL")
    elasticsearch_username: Optional[str] = Field(None, env="ELASTICSEARCH_USERNAME")
    elasticsearch_password: Optional[str] = Field(None, env="ELASTICSEARCH_PASSWORD")

    # Index名稱
    main_categories_index: str = Field("main_categories", env="MAIN_CATEGORIES_INDEX")
    sub_categories_index: str = Field("sub_categories", env="SUB_CATEGORIES_INDEX")

    # Embedding設定
    embedding_dims: int = Field(3072, env="EMBEDDING_DIMS")
    embedding_task_type: str = Field("RETRIEVAL_DOCUMENT", env="EMBEDDING_TASK_TYPE")

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


class AgnoSettings(BaseSettings):
    """Agno框架相關設定"""

    # Agno代理設定
    agent_config_dir: Path = Field(
        default_factory=lambda: Path.cwd() / "config" / "agents",
        description="代理設定檔目錄"
    )
    
    template_dir: Path = Field(
        default_factory=lambda: Path.cwd() / "templates",
        description="Jinja2模板目錄"
    )
    
    # LLM設定
    max_retries: int = Field(3, env="AGNO_MAX_RETRIES", description="最大重試次數")
    retry_delay: float = Field(1.0, env="AGNO_RETRY_DELAY", description="重試延遲秒數")
    timeout: int = Field(30, env="AGNO_TIMEOUT", description="請求超時秒數")
    
    # 分類設定
    confidence_threshold: float = Field(0.7, env="AGNO_CONFIDENCE_THRESHOLD", description="信心度門檻")
    concurrent_limit: int = Field(5, env="AGNO_CONCURRENT_LIMIT", description="並發限制")
    max_concurrent: int = Field(20, env="AGNO_MAX_CONCURRENT", description="最大並發數")
    
    # 批次處理設定
    batch_size: int = Field(50, env="AGNO_BATCH_SIZE", description="批次大小")
    memory_limit_gb: float = Field(1.0, env="AGNO_MEMORY_LIMIT_GB", description="記憶體限制(GB)")
    
    # 模板設定
    default_language: str = Field("zh-TW", env="AGNO_DEFAULT_LANGUAGE", description="預設語言")
    prompt_templates: Dict[str, str] = Field(
        default_factory=lambda: {
            "classification": "classification.yaml",
            "intent_detection": "intent_detection.yaml",
            "confidence_assessment": "confidence_assessment.yaml",
            "simple_classification": "simple_classification.yaml"
        },
        description="提示模板映射"
    )

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        extra = "allow"
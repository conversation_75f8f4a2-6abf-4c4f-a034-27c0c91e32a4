from datetime import datetime
from typing import List, Optional, Dict, Any
from enum import Enum
from pydantic import BaseModel, Field, field_validator
import uuid


class IntentType(str, Enum):
    """投訴意圖類型 - Traditional Chinese complaint intent types"""
    
    # 服務品質相關
    SERVICE_COMPLAINT = "服務投訴"
    SERVICE_QUALITY = "服務品質"
    STAFF_ATTITUDE = "員工態度"
    RESPONSE_TIME = "回應時間"
    
    # 產品相關
    PRODUCT_DEFECT = "產品瑕疵"
    PRODUCT_QUALITY = "產品品質"
    WARRANTY_ISSUE = "保固問題"
    
    # 流程相關
    PROCESS_ISSUE = "流程問題"
    PROCEDURE_COMPLAINT = "程序投訴"
    POLICY_DISPUTE = "政策爭議"
    
    # 費用相關
    BILLING_ISSUE = "計費問題"
    REFUND_REQUEST = "退費要求"
    
    # 其他
    INFORMATION_REQUEST = "資訊查詢"
    GENERAL_FEEDBACK = "一般回饋"


class ConfidenceLevel(str, Enum):
    """信心度等級"""
    
    HIGH = "high"      # >= 0.8
    MEDIUM = "medium"  # 0.5-0.8
    LOW = "low"        # < 0.5


class ComplaintInput(BaseModel):
    """投訴輸入實體"""
    
    id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="唯一識別碼")
    content: str = Field(..., min_length=1, max_length=10000, description="投訴內容")
    source: Optional[str] = Field(None, description="來源系統或管道")
    reference_id: Optional[str] = Field(None, description="外部參考ID")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="附加元資料")
    created_at: datetime = Field(default_factory=datetime.now, description="創建時間")
    
    @field_validator('content')
    @classmethod
    def validate_content(cls, v: str) -> str:
        """驗證投訴內容不為空且符合基本要求"""
        if not v or not v.strip():
            raise ValueError("投訴內容不能為空")
        
        # 檢查是否包含基本中文字符
        chinese_chars = sum(1 for char in v if '\u4e00' <= char <= '\u9fff')
        if chinese_chars < 5:  # 至少5個中文字符
            raise ValueError("投訴內容應包含足夠的中文描述")
            
        return v.strip()


class RAGCandidate(BaseModel):
    """RAG檢索候選結果"""
    
    id: str = Field(..., description="候選項目ID")
    main_category: str = Field(..., description="主案類")
    sub_category: str = Field(..., description="子案類")
    description: str = Field(..., description="案類描述")
    combined_text: str = Field(..., description="組合文本")
    keywords: List[str] = Field(default_factory=list, description="關鍵詞列表")
    similarity_score: float = Field(..., ge=0.0, le=1.0, description="相似度分數")
    rank: int = Field(..., ge=1, description="排名")
    
    class Config:
        json_encoders = {
            float: lambda v: round(v, 4)
        }


class ClassificationResult(BaseModel):
    """分類結果實體"""
    
    complaint_id: str = Field(..., description="投訴ID")
    predicted_intent: IntentType = Field(..., description="預測意圖")
    confidence_score: float = Field(..., ge=0.0, le=1.0, description="信心度分數")
    confidence_level: ConfidenceLevel = Field(..., description="信心度等級")
    
    # RAG檢索結果
    rag_candidates: List[RAGCandidate] = Field(
        default_factory=list, 
        max_length=10,
        description="RAG檢索候選列表（最多10個）"
    )
    
    # 分類詳細資訊
    predicted_main_category: Optional[str] = Field(None, description="預測主案類")
    predicted_sub_category: Optional[str] = Field(None, description="預測子案類")
    reasoning: Optional[str] = Field(None, description="分類推理過程")
    
    # 處理資訊
    processing_time_ms: int = Field(..., ge=0, description="處理時間（毫秒）")
    rag_retrieval_time_ms: int = Field(0, ge=0, description="RAG檢索時間（毫秒）")
    llm_classification_time_ms: int = Field(0, ge=0, description="LLM分類時間（毫秒）")
    
    # 錯誤資訊
    has_error: bool = Field(False, description="是否有錯誤")
    error_message: Optional[str] = Field(None, description="錯誤訊息")
    fallback_used: bool = Field(False, description="是否使用了回退策略")
    
    # 時間戳記
    processed_at: datetime = Field(default_factory=datetime.now, description="處理時間")
    
    @field_validator('confidence_score')
    @classmethod
    def set_confidence_level(cls, v: float) -> float:
        """驗證信心度分數範圍"""
        return round(v, 4)
    
    def __init__(self, **data):
        super().__init__(**data)
        # 根據信心度分數自動設定信心度等級
        if not hasattr(self, 'confidence_level') or self.confidence_level is None:
            if self.confidence_score >= 0.8:
                self.confidence_level = ConfidenceLevel.HIGH
            elif self.confidence_score >= 0.5:
                self.confidence_level = ConfidenceLevel.MEDIUM
            else:
                self.confidence_level = ConfidenceLevel.LOW


class BatchProcessingSummary(BaseModel):
    """批次處理摘要"""
    
    batch_id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="批次ID")
    total_complaints: int = Field(..., ge=0, description="總投訴數量")
    processed_count: int = Field(0, ge=0, description="已處理數量")
    success_count: int = Field(0, ge=0, description="成功處理數量")
    error_count: int = Field(0, ge=0, description="錯誤數量")
    
    # 性能統計
    total_processing_time_ms: int = Field(0, ge=0, description="總處理時間（毫秒）")
    average_processing_time_ms: float = Field(0.0, ge=0.0, description="平均處理時間（毫秒）")
    max_processing_time_ms: int = Field(0, ge=0, description="最大處理時間（毫秒）")
    min_processing_time_ms: int = Field(0, ge=0, description="最小處理時間（毫秒）")
    
    # 分類統計
    intent_distribution: Dict[IntentType, int] = Field(
        default_factory=dict, 
        description="意圖分布統計"
    )
    confidence_distribution: Dict[ConfidenceLevel, int] = Field(
        default_factory=dict,
        description="信心度分布統計"
    )
    
    # 記憶體使用統計
    peak_memory_mb: Optional[float] = Field(None, description="峰值記憶體使用（MB）")
    average_memory_mb: Optional[float] = Field(None, description="平均記憶體使用（MB）")
    
    # 錯誤統計
    error_types: Dict[str, int] = Field(default_factory=dict, description="錯誤類型統計")
    fallback_usage_count: int = Field(0, ge=0, description="回退策略使用次數")
    
    # 時間資訊
    started_at: datetime = Field(default_factory=datetime.now, description="開始時間")
    completed_at: Optional[datetime] = Field(None, description="完成時間")
    
    @property
    def success_rate(self) -> float:
        """成功率計算"""
        if self.processed_count == 0:
            return 0.0
        return round(self.success_count / self.processed_count, 4)
    
    @property
    def error_rate(self) -> float:
        """錯誤率計算"""
        if self.processed_count == 0:
            return 0.0
        return round(self.error_count / self.processed_count, 4)
    
    @property
    def processing_duration_seconds(self) -> Optional[float]:
        """處理持續時間（秒）"""
        if self.completed_at is None:
            return None
        return (self.completed_at - self.started_at).total_seconds()
    
    def add_result(self, result: ClassificationResult) -> None:
        """添加分類結果到摘要統計"""
        self.processed_count += 1
        
        if not result.has_error:
            self.success_count += 1
            # 更新意圖分布
            if result.predicted_intent in self.intent_distribution:
                self.intent_distribution[result.predicted_intent] += 1
            else:
                self.intent_distribution[result.predicted_intent] = 1
            
            # 更新信心度分布
            if result.confidence_level in self.confidence_distribution:
                self.confidence_distribution[result.confidence_level] += 1
            else:
                self.confidence_distribution[result.confidence_level] = 1
        else:
            self.error_count += 1
            # 更新錯誤類型統計
            error_type = result.error_message or "unknown_error"
            if error_type in self.error_types:
                self.error_types[error_type] += 1
            else:
                self.error_types[error_type] = 1
        
        # 更新回退策略使用統計
        if result.fallback_used:
            self.fallback_usage_count += 1
        
        # 更新處理時間統計
        processing_time = result.processing_time_ms
        self.total_processing_time_ms += processing_time
        
        if self.processed_count == 1:
            self.min_processing_time_ms = processing_time
            self.max_processing_time_ms = processing_time
        else:
            self.min_processing_time_ms = min(self.min_processing_time_ms, processing_time)
            self.max_processing_time_ms = max(self.max_processing_time_ms, processing_time)
        
        self.average_processing_time_ms = round(
            self.total_processing_time_ms / self.processed_count, 2
        )
    
    def finalize(self) -> None:
        """完成批次處理，設定完成時間"""
        self.completed_at = datetime.now()

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            float: lambda v: round(v, 4)
        }
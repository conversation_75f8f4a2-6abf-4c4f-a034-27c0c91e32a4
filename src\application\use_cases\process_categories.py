import json
import re
import logging
from pathlib import Path
from typing import List, Dict, Any, Tuple
from src.domain.entities import MainCategory, SubCategory
from src.domain.interfaces import EmbedderInterface, RepositoryInterface
from src.infrastructure.exceptions import ElasticsearchBulkError, ElasticsearchConnectionError

# 設定日誌
logger = logging.getLogger(__name__)


class ProcessCategoriesUseCase:
    """處理案類資料的Use Case"""

    def __init__(self, embedder: EmbedderInterface, repository: RepositoryInterface):
        self.embedder = embedder
        self.repository = repository

    async def execute(self, json_file_path: str) -> None:
        """
        執行案類資料處理流程

        Args:
            json_file_path: categories_def.json檔案路徑
        """
        # 讀取JSON資料
        categories_data = self._load_json_file(json_file_path)

        # 處理資料
        main_categories, sub_categories = self._parse_categories(categories_data)

        # 創建索引
        print("創建Elasticsearch索引...")
        await self.repository.create_indices()

        # 生成主案類embeddings
        print(f"生成{len(main_categories)}個主案類的embeddings...")
        main_texts = [cat.combined_text for cat in main_categories]
        main_embeddings = await self.embedder.generate_embeddings_batch(main_texts[:1])

        # 更新主案類的embeddings
        for cat, embedding in zip(main_categories, main_embeddings):
            cat.embedding = embedding

        # 生成子案類embeddings
        print(f"生成{len(sub_categories)}個子案類的embeddings...")
        sub_texts = [cat.combined_text for cat in sub_categories]
        sub_embeddings = await self.embedder.generate_embeddings_batch(sub_texts[:1])

        # 更新子案類的embeddings
        for cat, embedding in zip(sub_categories, sub_embeddings):
            cat.embedding = embedding

        # 儲存到Elasticsearch
        try:
            print("儲存主案類到Elasticsearch...")
            await self.repository.save_main_categories_batch(main_categories[:1])

        except ElasticsearchBulkError as e:
            logger.error(f"儲存主案類時發生錯誤: {e.message}")
            print(f"\n❌ 儲存主案類失敗:")
            print(f"   - 成功: {e.success_count} 筆")
            print(f"   - 失敗: {e.failed_count} 筆")

            # 顯示失敗詳情
            failed_docs = e.get_failed_documents()
            if failed_docs:
                print("\n失敗文件詳情:")
                for doc in failed_docs[:5]:  # 顯示前5筆
                    print(f"   - ID: {doc['document_id']}")
                    print(f"     錯誤類型: {doc['error_type']}")
                    print(f"     錯誤原因: {doc['error_reason']}")

                if len(failed_docs) > 5:
                    print(f"   ... 還有 {len(failed_docs) - 5} 筆失敗文件")

            # 決定是否繼續
            if e.success_count == 0:
                print("\n所有主案類都儲存失敗，停止處理。")
                raise
            else:
                print(f"\n部分主案類儲存成功 ({e.success_count} 筆)，繼續處理子案類...")

        try:
            print("\n儲存子案類到Elasticsearch...")
            await self.repository.save_sub_categories_batch(sub_categories[:1])

        except ElasticsearchBulkError as e:
            logger.error(f"儲存子案類時發生錯誤: {e.message}")
            print(f"\n❌ 儲存子案類失敗:")
            print(f"   - 成功: {e.success_count} 筆")
            print(f"   - 失敗: {e.failed_count} 筆")

            # 顯示失敗詳情
            failed_docs = e.get_failed_documents()
            if failed_docs:
                print("\n失敗文件詳情:")
                for doc in failed_docs[:5]:  # 顯示前5筆
                    print(f"   - ID: {doc['document_id']}")
                    print(f"     錯誤類型: {doc['error_type']}")
                    print(f"     錯誤原因: {doc['error_reason']}")

                if len(failed_docs) > 5:
                    print(f"   ... 還有 {len(failed_docs) - 5} 筆失敗文件")

            if e.success_count == 0:
                print("\n所有子案類都儲存失敗。")
                raise

        except ElasticsearchConnectionError as e:
            logger.error(f"Elasticsearch 連線錯誤: {e.message}")
            print(f"\n❌ 無法連線到 Elasticsearch: {e.details.get('host')}")
            print("\n💡 建議:")
            for suggestion in e.details.get('suggestions', []):
                print(f"   - {suggestion}")
            raise

        except Exception as e:
            logger.error(f"儲存資料時發生未預期的錯誤: {type(e).__name__}: {str(e)}")
            print(f"\n❌ 發生未預期的錯誤: {type(e).__name__}: {str(e)}")
            raise

        print("\n✅ 處理完成！")

    def _load_json_file(self, file_path: str) -> Dict[str, Any]:
        """載入JSON檔案"""
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)

    def _parse_categories(self, data: Dict[str, Any]) -> Tuple[List[MainCategory], List[SubCategory]]:
        """解析案類資料"""
        main_categories = []
        sub_categories = []

        for main_cat_name, main_cat_data in data.items():
            # 處理主案類
            combined_text = self._create_main_category_combined_text(main_cat_name, main_cat_data['description'])
            main_category = MainCategory(
                main_category=main_cat_name,
                description=main_cat_data['description'],
                combined_text=combined_text,
                sub_categories=list(main_cat_data['sub_categories'].keys())
            )
            main_categories.append(main_category)

            # 處理子案類
            for sub_cat_name, sub_cat_data in main_cat_data['sub_categories'].items():
                # 提取關鍵詞
                keywords = self._extract_keywords(sub_cat_data['description'])

                # 創建組合文本
                combined_text = self._create_sub_category_combined_text(
                    main_cat_name,
                    sub_cat_name,
                    sub_cat_data['description'],
                    main_cat_data['description']
                )

                sub_category = SubCategory(
                    main_category=main_cat_name,
                    sub_category=sub_cat_name,
                    sub_description=sub_cat_data['description'],
                    main_description=main_cat_data['description'],
                    combined_text=combined_text,
                    keywords=keywords
                )
                sub_categories.append(sub_category)

        return main_categories, sub_categories

    def _create_main_category_combined_text(self, category_name: str, description: str) -> str:
        """創建主案類的結構化embedding文本"""
        return f"主案類: {category_name}\n描述: {description}"

    def _create_sub_category_combined_text(self, main_cat: str, sub_cat: str,
                            sub_desc: str, main_desc: str) -> str:
        """創建子案類的結構化embedding文本"""
        return f"子案類: {sub_cat}\n描述: {sub_desc}\n所屬主案類: {main_cat}"

    def _extract_keywords(self, text: str) -> List[str]:
        """從描述中提取關鍵詞"""
        # 簡單的關鍵詞提取邏輯
        keywords = []

        # 提取括號內的內容作為關鍵詞
        bracket_contents = re.findall(r'（([^）]+)）', text)
        keywords.extend(bracket_contents)

        # 提取一些常見的關鍵詞模式
        patterns = [
            r'檢舉(\S+)',
            r'(\S+)問題',
            r'(\S+)管理',
            r'(\S+)申訴',
            r'(\S+)服務'
        ]

        for pattern in patterns:
            matches = re.findall(pattern, text)
            keywords.extend(matches)

        # 去重並過濾
        keywords = list(set(keywords))
        keywords = [k for k in keywords if len(k) > 1 and len(k) < 10]

        return keywords[:10]  # 限制最多10個關鍵詞
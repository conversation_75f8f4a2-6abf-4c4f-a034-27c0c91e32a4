from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any, Tuple
from ..entities.complaint_classification import RAGCandidate


class RAGRetrieverInterface(ABC):
    """RAG檢索器介面 - 定義向量相似度檢索功能"""
    
    @abstractmethod
    async def retrieve_similar_categories(
        self,
        query_text: str,
        top_k: int = 10,
        similarity_threshold: float = 0.75,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[RAGCandidate]:
        """
        檢索相似案類
        
        Args:
            query_text: 查詢文本（投訴內容）
            top_k: 返回最相似的K個結果
            similarity_threshold: 相似度門檻
            filters: 額外篩選條件（可選）
            
        Returns:
            相似案類候選列表，按相似度降序排列
            
        Raises:
            RetrievalError: 檢索過程中發生錯誤
        """
        pass
    
    @abstractmethod
    async def retrieve_with_fallback(
        self,
        query_text: str,
        primary_threshold: float = 0.75,
        fallback_threshold: float = 0.5,
        top_k: int = 10
    ) -> <PERSON><PERSON>[List[RAGCandidate], bool]:
        """
        帶回退策略的檢索
        
        Args:
            query_text: 查詢文本
            primary_threshold: 主要相似度門檻
            fallback_threshold: 回退相似度門檻
            top_k: 返回結果數量
            
        Returns:
            (候選列表, 是否使用了回退策略)
            
        Raises:
            RetrievalError: 檢索過程中發生錯誤
        """
        pass
    
    @abstractmethod
    async def batch_retrieve(
        self,
        query_texts: List[str],
        top_k: int = 10,
        similarity_threshold: float = 0.75,
        concurrency_limit: int = 5
    ) -> List[List[RAGCandidate]]:
        """
        批次檢索
        
        Args:
            query_texts: 查詢文本列表
            top_k: 每個查詢返回的結果數量
            similarity_threshold: 相似度門檻
            concurrency_limit: 並發限制
            
        Returns:
            每個查詢對應的候選列表
            
        Raises:
            BatchRetrievalError: 批次檢索錯誤
        """
        pass
    
    @abstractmethod
    async def get_category_by_id(self, category_id: str) -> Optional[RAGCandidate]:
        """
        根據ID取得特定案類
        
        Args:
            category_id: 案類ID
            
        Returns:
            案類候選項目，如果不存在則返回None
        """
        pass
    
    @abstractmethod
    async def get_embedding_stats(self) -> Dict[str, Any]:
        """
        取得嵌入向量統計資訊
        
        Returns:
            統計資訊，包含：
            - total_documents: 總文檔數
            - embedding_dimensions: 嵌入維度
            - index_size: 索引大小
            - last_updated: 最後更新時間
        """
        pass


class QueryEmbedderInterface(ABC):
    """查詢嵌入器介面 - 專門用於查詢文本的向量化"""
    
    @abstractmethod
    async def generate_query_embedding(self, query_text: str) -> List[float]:
        """
        生成查詢文本的向量嵌入
        
        Args:
            query_text: 查詢文本
            
        Returns:
            查詢向量嵌入
            
        Raises:
            EmbeddingError: 嵌入生成錯誤
        """
        pass
    
    @abstractmethod
    async def generate_query_embeddings_batch(
        self, 
        query_texts: List[str],
        batch_size: Optional[int] = None
    ) -> List[List[float]]:
        """
        批次生成查詢嵌入
        
        Args:
            query_texts: 查詢文本列表
            batch_size: 批次大小
            
        Returns:
            查詢向量嵌入列表
            
        Raises:
            EmbeddingError: 嵌入生成錯誤
        """
        pass
    
    @abstractmethod
    async def validate_query_text(self, query_text: str) -> bool:
        """
        驗證查詢文本是否適合生成嵌入
        
        Args:
            query_text: 查詢文本
            
        Returns:
            是否有效
        """
        pass


class SimilarityCalculatorInterface(ABC):
    """相似度計算器介面 - 定義向量相似度計算方法"""
    
    @abstractmethod
    def calculate_cosine_similarity(
        self, 
        vector_a: List[float], 
        vector_b: List[float]
    ) -> float:
        """
        計算餘弦相似度
        
        Args:
            vector_a: 向量A
            vector_b: 向量B
            
        Returns:
            餘弦相似度分數 (0-1)
        """
        pass
    
    @abstractmethod
    def calculate_batch_similarity(
        self,
        query_vector: List[float],
        candidate_vectors: List[List[float]]
    ) -> List[float]:
        """
        批次計算相似度
        
        Args:
            query_vector: 查詢向量
            candidate_vectors: 候選向量列表
            
        Returns:
            相似度分數列表
        """
        pass
    
    @abstractmethod
    def find_top_k_similar(
        self,
        query_vector: List[float],
        candidate_vectors: List[List[float]],
        candidate_metadata: List[Dict[str, Any]],
        k: int = 10,
        threshold: float = 0.0
    ) -> List[Tuple[int, float, Dict[str, Any]]]:
        """
        找出最相似的K個項目
        
        Args:
            query_vector: 查詢向量
            candidate_vectors: 候選向量列表
            candidate_metadata: 候選項目元資料
            k: 返回數量
            threshold: 相似度門檻
            
        Returns:
            (索引, 相似度分數, 元資料) 的元組列表
        """
        pass


class VectorStoreInterface(ABC):
    """向量儲存介面 - 定義向量資料庫操作"""
    
    @abstractmethod
    async def search_vectors(
        self,
        query_vector: List[float],
        index_name: str,
        top_k: int = 10,
        score_threshold: float = 0.0,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        向量相似度搜索
        
        Args:
            query_vector: 查詢向量
            index_name: 索引名稱
            top_k: 返回數量
            score_threshold: 分數門檻
            filters: 篩選條件
            
        Returns:
            搜索結果列表
        """
        pass
    
    @abstractmethod
    async def get_vector_by_id(
        self, 
        document_id: str, 
        index_name: str
    ) -> Optional[Dict[str, Any]]:
        """
        根據ID取得向量文檔
        
        Args:
            document_id: 文檔ID
            index_name: 索引名稱
            
        Returns:
            文檔資料，如果不存在則返回None
        """
        pass
    
    @abstractmethod
    async def check_index_exists(self, index_name: str) -> bool:
        """
        檢查索引是否存在
        
        Args:
            index_name: 索引名稱
            
        Returns:
            索引是否存在
        """
        pass
    
    @abstractmethod
    async def get_index_stats(self, index_name: str) -> Dict[str, Any]:
        """
        取得索引統計資訊
        
        Args:
            index_name: 索引名稱
            
        Returns:
            索引統計資訊
        """
        pass
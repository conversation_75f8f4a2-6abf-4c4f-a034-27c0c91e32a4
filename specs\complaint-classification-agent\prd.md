# Product Requirements Document: Complaint Classification Agent

## 1. Introduction/Overview

The Complaint Classification Agent is an AI-powered system that automatically classifies Taiwan government citizen complaints into appropriate categories while analyzing their underlying intents. This feature builds upon the existing RAG infrastructure to provide a critical automated routing system that processes citizen complaints in real-time, ensuring efficient resource allocation and improved citizen service experience.

**Goal**: Deliver a high-accuracy, multi-intent aware classification system that processes citizen complaints through a command-line interface with extensible architecture, providing confidence-scored categorization with comprehensive fallback mechanisms.

## 2. Goals

1. **High Accuracy Classification**: Achieve >90% accuracy in complaint category assignment using semantic similarity matching (>0.75 threshold, fallback to 0.6)
2. **Multi-Intent Analysis**: Detect and classify all relevant intents per complaint with equal weighting (no prioritization)
3. **Performance Excellence**: Process individual complaints in <2 seconds with concurrent processing capabilities
4. **Robust Error Handling**: Handle edge cases including short complaints, non-Chinese text, and ambiguous content with appropriate fallback strategies
5. **Extensible Architecture**: Design CLI interface that can accommodate future REST API, web UI, or batch processing interfaces
6. **Comprehensive Monitoring**: Track performance metrics, confidence distributions, and category patterns for continuous improvement

## 3. User Stories

### Primary Users: Government Administrative Staff
- **As a government clerk**, I want to upload a JSON file containing multiple complaints so that I can classify them all at once during busy periods
- **As a government administrator**, I want to see confidence scores for each classification so that I can prioritize manual review for low-confidence cases
- **As a quality assurance supervisor**, I want to review flagged low-confidence classifications so that I can ensure classification accuracy

### Secondary Users: System Administrators
- **As a system administrator**, I want to monitor processing performance and error rates so that I can optimize system capacity
- **As a data analyst**, I want to access classification patterns and trends so that I can improve government services based on citizen feedback

### Edge Case Scenarios
- **As a user**, I want the system to handle complaints that don't match any category well by providing the best available match with appropriate confidence flagging
- **As a user**, I want the system to detect multiple intents in complex complaints (e.g., both complaining about a problem AND requesting improvement)

## 4. Functional Requirements

### Core Classification Engine
1. **Vector Similarity Matching**: The system must generate embeddings using gemini-embedding-001 with task_type="RETRIEVAL_QUERY" and query Elasticsearch for similarity matches
2. **Category Assignment**: The system must assign complaints to specific sub-categories with corresponding main categories based on highest similarity scores
3. **Confidence Scoring**: The system must calculate and assign confidence levels (high/medium/low) based on match quality, complaint clarity, and consistency across categories
4. **Multi-Intent Detection**: The system must analyze complaints for multiple intents using the complete enum set: 檢舉告發, 請求協助, 抱怨問題, 請求改善, 諮詢問題, 不滿服務態度, 不滿人員專業度, 感謝讚美, 提出建議

### Input/Output Processing
5. **JSON Batch Processing**: The system must accept JSON files containing arrays of complaints with unique identifiers
6. **Structured Output**: The system must output JSON responses with category, main_category, intents array, confidence, and reason fields following strict Pydantic validation
7. **Command Line Interface**: The system must provide a CLI tool that accepts file paths and configuration options

### Error Handling & Fallback
8. **Similarity Threshold Management**: When no categories exceed 0.75 similarity, the system must lower the threshold to 0.6 and mark as low confidence
9. **Fallback Category Assignment**: The system must assign to general/other categories when no suitable matches are found above minimum thresholds
10. **Edge Case Processing**: The system must handle short complaints (detect insufficient content), non-Chinese text (detect language mismatch), and spam detection with appropriate error messages

### Performance & Concurrency
11. **Individual Processing Speed**: The system must process each complaint within 2 seconds including embedding generation and similarity search
12. **Concurrent Processing**: The system must support concurrent processing of multiple complaints within a batch file
13. **Performance Monitoring**: The system must track and log response times, confidence score distributions, and category assignment patterns

## 5. Non-Goals (Out of Scope)

1. **Web User Interface**: This initial version will not include a web-based interface
2. **Real-time API Endpoints**: REST API functionality is planned for future phases
3. **Database Persistence**: Complaint data and results will not be stored persistently (processed and returned)
4. **User Authentication**: No user management or authentication system required
5. **Multi-language Support**: Only Traditional Chinese text processing required
6. **Intent Prioritization**: All detected intents have equal weight - no ranking or prioritization
7. **Historical Analytics Dashboard**: Advanced reporting and analytics beyond basic metrics logging
8. **Integration with External Government Systems**: Direct integration with existing government databases or case management systems

## 6. Technical Considerations

### Compatibility Check
**✅ Compatible**: The feature leverages existing infrastructure components:
- Gemini embedding-001 integration via GeminiEmbedder
- Elasticsearch 8.13.2 vector search via ElasticsearchRepository  
- Clean Architecture patterns with domain entities and use cases
- Async processing capabilities for concurrent operations

### Architecture Impact
**Minimal Impact**: New components follow existing patterns:
- New domain entities for complaint classification (ComplaintInput, ClassificationResult)
- New use case class (ClassifyComplaintsUseCase) in application layer
- New CLI interface extending existing main.py structure
- Reuse existing infrastructure adapters without modification

### Database Impact
**No Schema Changes Required**: 
- Utilizes existing sub_categories and main_categories indices
- No new Elasticsearch indices needed
- Leverages existing embeddings and similarity search capabilities
- Existing 3072-dimensional dense vectors support the classification queries

### Technical Constraints
- **Rate Limiting**: Gemini API quotas may limit concurrent processing speed
- **Memory Usage**: Large batch files may require memory management for embedding generation
- **Network Latency**: Elasticsearch query performance depends on network and cluster performance
- **Vector Search Performance**: 3072-dimensional cosine similarity searches have inherent computational costs

### Integration Requirements
- **Configuration Management**: Extend existing Settings class for classification-specific parameters
- **Error Handling**: Integrate with existing ElasticsearchConnectionError and ElasticsearchBulkError patterns
- **Logging**: Extend existing logging infrastructure for classification metrics and debugging

### Performance Implications
- **Concurrent Processing**: Async/await patterns support multiple complaint processing
- **Elasticsearch Optimization**: Utilize existing dense vector indices optimized for similarity search
- **Gemini API Efficiency**: Batch embedding generation where possible to minimize API calls
- **Memory Management**: Stream processing for large files to control memory usage

## 7. Success Metrics

### Accuracy Metrics
- **Classification Accuracy**: >90% correct category assignments validated against manual review samples
- **Confidence Correlation**: High confidence classifications should achieve >95% accuracy, medium confidence >85%, low confidence >70%
- **Intent Detection Precision**: >85% accuracy in detecting all relevant intents per complaint

### Performance Metrics  
- **Processing Speed**: Average processing time <2 seconds per complaint
- **Throughput**: Support processing of 100+ complaints in concurrent batches
- **System Availability**: <5% error rate across all processing scenarios

### User Experience Metrics
- **Low Confidence Rate**: <20% of complaints flagged as low confidence requiring manual review
- **Edge Case Handling**: <5% of complaints result in processing errors or system failures
- **Operational Efficiency**: Measurable reduction in manual classification workload for government staff

### Quality Metrics
- **Multi-Intent Coverage**: Complaints with multiple intents should have >80% of relevant intents correctly identified
- **Consistency**: Same complaint processed multiple times should yield identical results
- **Explainability**: Classification reasons should be clear and actionable for manual review

## 8. Open Questions

### Technical Implementation
1. **Batch Size Optimization**: What is the optimal batch size for concurrent processing balancing performance with memory usage?
2. **Confidence Threshold Tuning**: Should the confidence thresholds be configurable or require additional fine-tuning based on real data?
3. **Error Recovery**: What specific retry strategies should be implemented for Gemini API timeouts or Elasticsearch connection issues?

### Business Logic
4. **Intent Mutual Exclusivity Rules**: How should the system handle edge cases where "感謝讚美" appears alongside negative intents in complex complaints?
5. **Category Assignment Priority**: When multiple categories have similar high scores (e.g., 0.76 vs 0.75), what additional criteria should determine the final selection?
6. **Ambiguous Keyword Handling**: How should the system weight ambiguous keywords that appear across multiple categories?

### Operational Considerations
7. **Manual Review Workflow**: What specific information should be provided to support manual review of low-confidence classifications?
8. **Performance Monitoring**: What real-time monitoring and alerting capabilities are needed for production deployment?
9. **Data Quality Feedback**: How will classification accuracy feedback be collected and incorporated for continuous improvement?

### Future Extensibility
10. **API Interface Design**: What specific endpoints and request/response formats will be needed for the future REST API phase?
11. **Integration Points**: What data formats and interfaces should be designed to support future integration with government case management systems?
12. **Scalability Planning**: What architecture considerations are needed to support scaling to handle thousands of concurrent complaints?

---

**Document Version**: 1.0  
**Created**: 2025-08-06  
**Author**: Product Requirements Specification  
**Review Status**: Draft - Pending Technical Review
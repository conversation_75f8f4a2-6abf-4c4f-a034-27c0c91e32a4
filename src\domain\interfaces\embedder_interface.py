from abc import ABC, abstractmethod
from typing import List, Optional


class EmbedderInterface(ABC):
    """嵌入生成器介面"""
    
    @abstractmethod
    async def generate_embedding(self, text: str) -> List[float]:
        """
        生成文本的向量嵌入
        
        Args:
            text: 要生成嵌入的文本
            
        Returns:
            向量嵌入（浮點數列表）
        """
        pass
    
    @abstractmethod
    async def generate_embeddings_batch(self, texts: List[str], batch_size: Optional[int] = None) -> List[List[float]]:
        """
        批次生成多個文本的向量嵌入
        
        Args:
            texts: 要生成嵌入的文本列表
            batch_size: 批次大小，None表示全部併發處理，1表示逐筆處理
            
        Returns:
            向量嵌入列表
        """
        pass
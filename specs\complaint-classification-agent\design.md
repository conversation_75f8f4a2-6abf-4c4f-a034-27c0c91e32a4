# Complaint Classification Agent - Technical Design

## 1. System Architecture Overview

### 1.1 High-Level Architecture

```mermaid
graph TD
    A[CLI Interface] --> B[ClassifyComplaintsUseCase]
    B --> C[RAG Retrieval Layer]
    B --> D[Agno Agent Layer]
    
    C --> E[GeminiEmbedder]
    C --> F[ElasticsearchRepository]
    
    D --> G[ComplaintClassifierAgent]
    G --> H[AgnoLLMInterface]
    G --> I[PromptTemplateManager]
    
    H --> J[Gemini Chat API]
    E --> K[Gemini Embedding API]
    F --> L[Elasticsearch]
    
    M[Configuration Layer] --> B
    M --> D
    M --> C
```

### 1.2 RAG + Agno + LLM Dual-Stage Workflow

```mermaid
sequenceDiagram
    participant CLI as CLI Interface
    participant UC as ClassifyComplaintsUseCase
    participant RAG as RAG Retrieval Layer
    participant EMB as GeminiEmbedder
    participant ES as ElasticsearchRepository
    participant AGENT as ComplaintClassifierAgent
    participant LL<PERSON> as AgnoLLMInterface
    participant GE<PERSON><PERSON> as Gemini Chat API
    
    CLI->>UC: process_complaints(batch)
    
    loop For each complaint
        UC->>RAG: retrieve_candidates(complaint_text)
        RAG->>EMB: generate_embedding(text, task="RETRIEVAL_QUERY")
        EMB->>GEMINI: embed_content()
        GEMINI-->>EMB: embedding_vector
        EMB-->>RAG: query_embedding
        
        RAG->>ES: similarity_search(embedding, limit=10)
        ES-->>RAG: candidate_categories
        RAG-->>UC: rag_candidates
        
        UC->>AGENT: classify_with_context(complaint, candidates)
        AGENT->>LLM: process_classification(prompt_data)
        LLM->>GEMINI: chat_completion()
        GEMINI-->>LLM: classification_result
        LLM-->>AGENT: structured_output
        AGENT-->>UC: classification_result
        
        UC-->>CLI: complaint_result
    end
    
    CLI-->>CLI: aggregate_results()
```

## 2. Component Design

### 2.1 Domain Layer Extensions

#### 2.1.1 New Domain Entities

```python
# src/domain/entities/complaint_classification.py
from datetime import datetime
from enum import Enum
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field, validator

class IntentType(str, Enum):
    """民眾陳情意圖類型枚舉"""
    REPORT_VIOLATION = "檢舉告發"        # 舉報違法違規行為
    REQUEST_ASSISTANCE = "請求協助"       # 需要政府單位幫忙解決問題
    COMPLAIN_PROBLEM = "抱怨問題"        # 對現況表達不滿
    REQUEST_IMPROVEMENT = "請求改善"      # 要求改善現有狀況或制度
    INQUIRE_INFORMATION = "諮詢問題"      # 詢問相關資訊或程序
    DISSATISFIED_SERVICE = "不滿服務態度" # 對公務人員服務態度不滿
    DISSATISFIED_COMPETENCE = "不滿人員專業度" # 對公務人員專業能力不滿
    GRATITUDE_PRAISE = "感謝讚美"        # 表達感謝或讚美
    PROVIDE_SUGGESTION = "提出建議"       # 提供改善建議或意見
    APPLY_SERVICE = "申請服務"           # 申請各種公共服務或證件
    REQUEST_COMPENSATION = "要求賠償"      # 因政府行為造成損失而要求賠償
    EMERGENCY_HELP = "緊急求助"          # 需要立即處理的緊急狀況
    INFO_DISCLOSURE = "資訊公開"         # 要求公開政府資訊或資料
    OTHER = "其他"                      # 無法歸類至上述任何類別的意圖

class ConfidenceLevel(str, Enum):
    """信心程度枚舉"""
    HIGH = "high"
    LOW = "low"

class ComplaintInput(BaseModel):
    """陳情輸入資料模型"""
    id: str = Field(..., description="陳情唯一識別碼")
    content: str = Field(..., min_length=10, description="陳情內容文本")
    metadata: Optional[Dict[str, Any]] = Field(None, description="額外元資料")
    
    @validator('content')
    def validate_content(cls, v):
        if not v or v.strip() == "":
            raise ValueError("陳情內容不能為空")
        return v.strip()

class RAGCandidate(BaseModel):
    """RAG檢索候選類別模型"""
    main_category: str = Field(..., description="主案類名稱")
    sub_category: str = Field(..., description="子案類名稱")
    similarity_score: float = Field(..., ge=0.0, le=1.0, description="相似度分數")
    sub_description: str = Field(..., description="子案類描述")
    keywords: List[str] = Field(default_factory=list, description="關鍵詞")

class ClassificationResult(BaseModel):
    """分類結果模型"""
    complaint_id: str = Field(..., description="陳情識別碼")
    main_category: str = Field(..., description="分類主案類")
    sub_category: str = Field(..., description="分類子案類")
    intents: List[IntentType] = Field(..., description="檢測到的意圖列表")
    confidence: ConfidenceLevel = Field(..., description="分類信心程度")
    similarity_score: float = Field(..., ge=0.0, le=1.0, description="最高相似度分數")
    reasoning: str = Field(..., description="分類推理說明")
    processing_time_ms: int = Field(..., description="處理時間(毫秒)")
    rag_candidates: List[RAGCandidate] = Field(..., description="RAG檢索候選清單")
    created_at: datetime = Field(default_factory=datetime.now, description="結果產生時間")
    
    class Config:
        use_enum_values = True

class BatchProcessingSummary(BaseModel):
    """批次處理摘要模型"""
    total_processed: int = Field(..., description="總處理數量")
    high_confidence: int = Field(..., description="高信心數量")
    low_confidence: int = Field(..., description="低信心數量")
    errors: int = Field(..., description="錯誤數量")
    average_processing_time_ms: float = Field(..., description="平均處理時間")
    intent_distribution: Dict[str, int] = Field(..., description="意圖分布統計")
    category_distribution: Dict[str, int] = Field(..., description="類別分布統計")
```

#### 2.1.2 New Domain Interfaces

```python
# src/domain/interfaces/agent_interface.py
from abc import ABC, abstractmethod
from typing import List
from src.domain.entities.complaint_classification import ComplaintInput, ClassificationResult, RAGCandidate

class ComplaintClassifierInterface(ABC):
    """陳情分類器抽象介面"""
    
    @abstractmethod
    async def classify_complaint(
        self, 
        complaint: ComplaintInput, 
        rag_candidates: List[RAGCandidate]
    ) -> ClassificationResult:
        """
        使用RAG候選與LLM進行陳情分類
        
        Args:
            complaint: 陳情輸入資料
            rag_candidates: RAG檢索的候選類別列表
            
        Returns:
            分類結果
        """
        pass

# src/domain/interfaces/rag_interface.py
from abc import ABC, abstractmethod
from typing import List
from src.domain.entities.complaint_classification import RAGCandidate

class RAGRetrieverInterface(ABC):
    """RAG檢索器抽象介面"""
    
    @abstractmethod
    async def retrieve_candidates(
        self, 
        complaint_text: str, 
        limit: int = 10,
        similarity_threshold: float = 0.6
    ) -> List[RAGCandidate]:
        """
        從向量資料庫檢索相似的案類候選
        
        Args:
            complaint_text: 陳情文本
            limit: 返回候選數量限制
            similarity_threshold: 相似度閾值
            
        Returns:
            候選類別列表
        """
        pass
```

### 2.2 Application Layer - Use Cases

#### 2.2.1 Main Classification Use Case

```python
# src/application/use_cases/classify_complaints.py
import asyncio
from typing import List, Dict, Any
from datetime import datetime
import logging

from src.domain.entities.complaint_classification import (
    ComplaintInput, ClassificationResult, BatchProcessingSummary, ConfidenceLevel
)
from src.domain.interfaces.agent_interface import ComplaintClassifierInterface
from src.domain.interfaces.rag_interface import RAGRetrieverInterface

logger = logging.getLogger(__name__)

class ClassifyComplaintsUseCase:
    """陳情分類用例"""
    
    def __init__(
        self,
        rag_retriever: RAGRetrieverInterface,
        complaint_classifier: ComplaintClassifierInterface,
        max_concurrency: int = 5
    ):
        self.rag_retriever = rag_retriever
        self.complaint_classifier = complaint_classifier
        self.max_concurrency = max_concurrency
        self.semaphore = asyncio.Semaphore(max_concurrency)
    
    async def process_single_complaint(self, complaint: ComplaintInput) -> ClassificationResult:
        """
        處理單一陳情分類
        
        Args:
            complaint: 陳情輸入資料
            
        Returns:
            分類結果
        """
        async with self.semaphore:
            start_time = datetime.now()
            
            try:
                # Stage 1: RAG Retrieval
                logger.info(f"開始RAG檢索: {complaint.id}")
                rag_candidates = await self.rag_retriever.retrieve_candidates(complaint.content)
                
                if not rag_candidates:
                    logger.warning(f"未找到RAG候選: {complaint.id}")
                    # Fallback to lower threshold
                    rag_candidates = await self.rag_retriever.retrieve_candidates(
                        complaint.content, 
                        similarity_threshold=0.4
                    )
                
                # Stage 2: Agno Agent Classification  
                logger.info(f"開始Agno代理分類: {complaint.id}")
                result = await self.complaint_classifier.classify_complaint(
                    complaint, rag_candidates
                )
                
                # Calculate processing time
                processing_time = (datetime.now() - start_time).total_seconds() * 1000
                result.processing_time_ms = int(processing_time)
                
                logger.info(
                    f"分類完成: {complaint.id} - {result.sub_category} "
                    f"({result.confidence}) - {processing_time:.0f}ms"
                )
                
                return result
                
            except Exception as e:
                logger.error(f"分類失败: {complaint.id} - {str(e)}")
                # Return error result
                return self._create_error_result(complaint, str(e), start_time)
    
    async def process_batch(self, complaints: List[ComplaintInput]) -> Dict[str, Any]:
        """
        批次處理陳情分類
        
        Args:
            complaints: 陳情列表
            
        Returns:
            包含結果和摘要的字典
        """
        logger.info(f"開始批次處理: {len(complaints)} 個陳情")
        
        # Process all complaints concurrently
        tasks = [
            self.process_single_complaint(complaint) 
            for complaint in complaints
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Separate successful results from exceptions
        successful_results = []
        error_count = 0
        
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"處理異常: {complaints[i].id} - {str(result)}")
                error_count += 1
            else:
                successful_results.append(result)
        
        # Generate summary
        summary = self._generate_summary(successful_results, error_count)
        
        logger.info(f"批次處理完成: {len(successful_results)} 成功, {error_count} 錯誤")
        
        return {
            "results": successful_results,
            "summary": summary
        }
    
    def _create_error_result(
        self, 
        complaint: ComplaintInput, 
        error_message: str, 
        start_time: datetime
    ) -> ClassificationResult:
        """創建錯誤結果"""
        processing_time = (datetime.now() - start_time).total_seconds() * 1000
        
        return ClassificationResult(
            complaint_id=complaint.id,
            main_category="其他",
            sub_category="系統錯誤",
            intents=["其他"],
            confidence=ConfidenceLevel.LOW,
            similarity_score=0.0,
            reasoning=f"處理錯誤: {error_message}",
            processing_time_ms=int(processing_time),
            rag_candidates=[]
        )
    
    def _generate_summary(
        self, 
        results: List[ClassificationResult], 
        error_count: int
    ) -> BatchProcessingSummary:
        """生成批次處理摘要"""
        if not results:
            return BatchProcessingSummary(
                total_processed=error_count,
                high_confidence=0,
                low_confidence=0,
                errors=error_count,
                average_processing_time_ms=0.0,
                intent_distribution={},
                category_distribution={}
            )
        
        high_confidence = sum(1 for r in results if r.confidence == ConfidenceLevel.HIGH)
        low_confidence = sum(1 for r in results if r.confidence == ConfidenceLevel.LOW)
        avg_time = sum(r.processing_time_ms for r in results) / len(results)
        
        # Intent distribution
        intent_dist = {}
        for result in results:
            for intent in result.intents:
                intent_dist[intent] = intent_dist.get(intent, 0) + 1
        
        # Category distribution  
        category_dist = {}
        for result in results:
            category_dist[result.sub_category] = category_dist.get(result.sub_category, 0) + 1
        
        return BatchProcessingSummary(
            total_processed=len(results) + error_count,
            high_confidence=high_confidence,
            low_confidence=low_confidence,
            errors=error_count,
            average_processing_time_ms=avg_time,
            intent_distribution=intent_dist,
            category_distribution=category_dist
        )
```

### 2.3 Infrastructure Layer - Agno Integration

#### 2.3.1 RAG Retriever Implementation

```python
# src/infrastructure/retrievers/rag_retriever.py
from typing import List
import logging

from src.domain.interfaces.rag_interface import RAGRetrieverInterface
from src.domain.interfaces.embedder_interface import EmbedderInterface
from src.domain.interfaces.repository_interface import RepositoryInterface
from src.domain.entities.complaint_classification import RAGCandidate

logger = logging.getLogger(__name__)

class ElasticsearchRAGRetriever(RAGRetrieverInterface):
    """基於Elasticsearch的RAG檢索器實作"""
    
    def __init__(
        self, 
        embedder: EmbedderInterface,
        repository: RepositoryInterface
    ):
        self.embedder = embedder
        self.repository = repository
    
    async def retrieve_candidates(
        self, 
        complaint_text: str, 
        limit: int = 10,
        similarity_threshold: float = 0.6
    ) -> List[RAGCandidate]:
        """
        從Elasticsearch檢索相似的案類候選
        """
        try:
            # Generate query embedding with RETRIEVAL_QUERY task type
            logger.debug(f"生成查詢嵌入向量: {complaint_text[:50]}...")
            
            # Use different task type for query embeddings
            query_embedding = await self.embedder.generate_embedding_for_query(complaint_text)
            
            # Search similar subcategories  
            search_results = await self.repository.similarity_search(
                index_name="sub_categories",
                query_vector=query_embedding,
                limit=limit,
                min_score=similarity_threshold
            )
            
            # Convert to RAG candidates
            candidates = []
            for hit in search_results:
                source = hit['_source']
                similarity_score = hit['_score']
                
                if similarity_score >= similarity_threshold:
                    candidate = RAGCandidate(
                        main_category=source['main_category'],
                        sub_category=source['sub_category'],
                        similarity_score=similarity_score,
                        sub_description=source['sub_description'],
                        keywords=source.get('keywords', [])
                    )
                    candidates.append(candidate)
            
            logger.info(f"RAG檢索完成: 找到 {len(candidates)} 個候選")
            return candidates
            
        except Exception as e:
            logger.error(f"RAG檢索失敗: {str(e)}")
            return []
```

#### 2.3.2 Agno Agent Implementation

```python
# src/infrastructure/agents/complaint_classifier_agent.py
import json
import asyncio
from typing import List, Dict, Any
from datetime import datetime
import logging

from agno import Agent, LLMInterface, PromptTemplate
from agno.exceptions import AgnoException, RetryableError

from src.domain.interfaces.agent_interface import ComplaintClassifierInterface
from src.domain.entities.complaint_classification import (
    ComplaintInput, ClassificationResult, RAGCandidate, 
    ConfidenceLevel, IntentType
)
from src.config import Settings

logger = logging.getLogger(__name__)

class ComplaintClassifierAgent(ComplaintClassifierInterface):
    """基於Agno框架的陳情分類代理"""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.agent = self._initialize_agent()
        self._setup_retry_config()
    
    def _initialize_agent(self) -> Agent:
        """初始化Agno代理"""
        agent_config = {
            "name": "complaint-classifier",
            "role": "台灣政府陳情分類專家",
            "model": "gemini-pro", 
            "temperature": 0.1,
            "max_tokens": 1500,
            "response_format": {"type": "json_object"}
        }
        
        # Initialize LLM interface with Gemini
        llm_interface = self._create_llm_interface()
        
        # Create agent with configuration
        agent = Agent(
            config=agent_config,
            llm_interface=llm_interface
        )
        
        # Load prompt templates
        self._load_prompt_templates(agent)
        
        return agent
    
    def _create_llm_interface(self) -> LLMInterface:
        """創建Gemini LLM介面"""
        from src.infrastructure.llm.agno_gemini_interface import AgnoGeminiInterface
        return AgnoGeminiInterface(self.settings)
    
    def _load_prompt_templates(self, agent: Agent):
        """載入提示詞模板"""
        # Classification template
        classification_template = PromptTemplate(
            name="classification",
            template=self._get_classification_template(),
            input_variables=["complaint_text", "rag_candidates"]
        )
        
        # Intent detection template
        intent_template = PromptTemplate(
            name="intent_detection", 
            template=self._get_intent_detection_template(),
            input_variables=["complaint_text", "intents_enum"]
        )
        
        # Confidence assessment template
        confidence_template = PromptTemplate(
            name="confidence_assessment",
            template=self._get_confidence_template(),
            input_variables=["complaint_text", "selected_category", "similarity_score"]
        )
        
        agent.add_templates([classification_template, intent_template, confidence_template])
    
    def _setup_retry_config(self):
        """設置重試配置"""
        self.retry_config = {
            "max_retries": 3,
            "backoff_factor": 2.0,
            "retry_on_errors": [RetryableError, TimeoutError],
            "fallback_enabled": True
        }
    
    async def classify_complaint(
        self, 
        complaint: ComplaintInput, 
        rag_candidates: List[RAGCandidate]
    ) -> ClassificationResult:
        """
        使用Agno代理進行陳情分類
        """
        try:
            start_time = datetime.now()
            
            # Prepare context for agent
            context = self._prepare_classification_context(complaint, rag_candidates)
            
            # Use Agno agent for classification with retry
            classification_result = await self._classify_with_retry(context)
            
            # Parse and validate result
            result = self._parse_classification_result(
                complaint, rag_candidates, classification_result, start_time
            )
            
            return result
            
        except Exception as e:
            logger.error(f"Agno分類失敗: {complaint.id} - {str(e)}")
            return self._create_fallback_result(complaint, rag_candidates, str(e))
    
    async def _classify_with_retry(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        使用重試機制進行分類
        """
        max_retries = self.retry_config["max_retries"]
        backoff_factor = self.retry_config["backoff_factor"]
        
        for attempt in range(max_retries + 1):
            try:
                # Primary classification attempt
                result = await self.agent.process(
                    template_name="classification",
                    context=context
                )
                
                # Validate result structure
                if self._validate_classification_result(result):
                    return result
                else:
                    raise ValueError("Invalid classification result structure")
                    
            except Exception as e:
                if attempt < max_retries:
                    wait_time = backoff_factor ** attempt
                    logger.warning(f"分類重試 {attempt + 1}/{max_retries}, 等待 {wait_time}s: {str(e)}")
                    await asyncio.sleep(wait_time)
                else:
                    logger.error(f"分類失敗，啟動降級策略: {str(e)}")
                    return await self._fallback_classification(context)
    
    async def _fallback_classification(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        降級分類策略
        """
        logger.info("使用降級分類策略")
        
        try:
            # Use simpler prompt for fallback
            simplified_context = {
                "complaint_text": context["complaint_text"],
                "top_candidate": context["rag_candidates"][0] if context["rag_candidates"] else None
            }
            
            # Simple classification with basic template
            result = await self.agent.process(
                template_name="simple_classification",
                context=simplified_context
            )
            
            return result
            
        except Exception as e:
            logger.error(f"降級策略也失敗: {str(e)}")
            # Final fallback - return basic classification
            return self._create_basic_fallback_result(context)
    
    def _prepare_classification_context(
        self, 
        complaint: ComplaintInput, 
        rag_candidates: List[RAGCandidate]
    ) -> Dict[str, Any]:
        """
        準備分類上下文
        """
        # Prepare RAG candidates context
        candidates_text = ""
        for i, candidate in enumerate(rag_candidates[:5], 1):  # Top 5 candidates
            candidates_text += f"""{i}. 主案類: {candidate.main_category}
   子案類: {candidate.sub_category}
   描述: {candidate.sub_description}
   相似度: {candidate.similarity_score:.3f}
   關鍵詞: {', '.join(candidate.keywords)}

"""
        
        # Intent types as string
        intent_types = [intent.value for intent in IntentType]
        
        return {
            "complaint_text": complaint.content,
            "complaint_id": complaint.id,
            "rag_candidates": candidates_text,
            "intent_types": intent_types,
            "candidate_count": len(rag_candidates)
        }
    
    def _validate_classification_result(self, result: Dict[str, Any]) -> bool:
        """
        驗證分類結果結構
        """
        required_fields = ["main_category", "sub_category", "intents", "confidence", "reasoning"]
        return all(field in result for field in required_fields)
    
    def _parse_classification_result(
        self,
        complaint: ComplaintInput,
        rag_candidates: List[RAGCandidate], 
        raw_result: Dict[str, Any],
        start_time: datetime
    ) -> ClassificationResult:
        """
        解析分類結果
        """
        processing_time = (datetime.now() - start_time).total_seconds() * 1000
        
        # Parse intents
        intents = []
        for intent_str in raw_result.get("intents", []):
            try:
                intent = IntentType(intent_str)
                intents.append(intent)
            except ValueError:
                logger.warning(f"未知意圖類型: {intent_str}")
        
        # Default to OTHER if no valid intents
        if not intents:
            intents = [IntentType.OTHER]
        
        # Parse confidence
        confidence_str = raw_result.get("confidence", "low").lower()
        confidence = ConfidenceLevel.HIGH if confidence_str == "high" else ConfidenceLevel.LOW
        
        # Get similarity score from best RAG candidate
        similarity_score = rag_candidates[0].similarity_score if rag_candidates else 0.0
        
        return ClassificationResult(
            complaint_id=complaint.id,
            main_category=raw_result.get("main_category", "其他"),
            sub_category=raw_result.get("sub_category", "其他"),
            intents=intents,
            confidence=confidence,
            similarity_score=similarity_score,
            reasoning=raw_result.get("reasoning", ""),
            processing_time_ms=int(processing_time),
            rag_candidates=rag_candidates
        )
    
    def _create_fallback_result(
        self,
        complaint: ComplaintInput,
        rag_candidates: List[RAGCandidate],
        error_message: str
    ) -> ClassificationResult:
        """
        創建降級結果
        """
        # Use best RAG candidate if available
        if rag_candidates:
            best_candidate = rag_candidates[0]
            main_category = best_candidate.main_category
            sub_category = best_candidate.sub_category
            similarity_score = best_candidate.similarity_score
        else:
            main_category = "其他"
            sub_category = "其他"
            similarity_score = 0.0
        
        return ClassificationResult(
            complaint_id=complaint.id,
            main_category=main_category,
            sub_category=sub_category,
            intents=[IntentType.OTHER],
            confidence=ConfidenceLevel.LOW,
            similarity_score=similarity_score,
            reasoning=f"系統降級分類: {error_message}",
            processing_time_ms=0,
            rag_candidates=rag_candidates
        )
    
    def _create_basic_fallback_result(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        創建基本降級結果
        """
        return {
            "main_category": "其他",
            "sub_category": "其他", 
            "intents": ["其他"],
            "confidence": "low",
            "reasoning": "系統無法正常分類，已降級至預設分類"
        }
    
    def _get_classification_template(self) -> str:
        """
        獲取分類提示詞模板
        """
        return """你是台灣政府陳情分類專家。請根據以下資訊對民眾陳情進行分類：

陳情內容：
{complaint_text}

RAG檢索到的相關案類候選（按相似度排序）：
{rag_candidates}

請執行以下任務：
1. 選擇最適合的案類（主案類和子案類）
2. 檢測所有相關的意圖類型
3. 評估分類信心程度
4. 提供分類理由

可選擇的意圖類型：
{intent_types}

請以JSON格式回應，包含以下欄位：
{
  "main_category": "選擇的主案類",
  "sub_category": "選擇的子案類", 
  "intents": ["檢測到的意圖列表"],
  "confidence": "high 或 low",
  "reasoning": "詳細的分類理由說明"
}

分類原則：
- 優先選擇相似度最高且語義最匹配的案類
- 可以檢測多個意圖，不需要排序
- 當相似度 >= 0.75 且語義明確匹配時，confidence 為 high
- 當相似度 < 0.75 或語義模糊時，confidence 為 low
- 必須提供清晰的分類理由"""

    def _get_intent_detection_template(self) -> str:
        """
        獲取意圖檢測提示詞模板
        """
        return """請分析以下陳情文本，檢測其中包含的所有意圖類型：

陳情內容：
{complaint_text}

可選擇的意圖類型：
{intents_enum}

請以JSON格式回應，列出所有檢測到的意圖：
{
  "intents": ["意圖1", "意圖2", ...],
  "reasoning": "意圖檢測的詳細說明"
}

檢測原則：
- 一個陳情可能包含多種意圖
- 仔細分析文本語義，不要遺漏任何明顯的意圖
- 如果無法確定具體意圖，選擇"其他"
- 提供檢測到每個意圖的具體理由"""
    
    def _get_confidence_template(self) -> str:
        """
        獲取信心評估提示詞模板
        """
        return """請評估以下分類結果的信心程度：

陳情內容：{complaint_text}
選擇的案類：{selected_category}
相似度分數：{similarity_score}

請根據以下標準評估信心程度：
- high: 相似度 >= 0.75 且語義明確匹配
- low: 相似度 < 0.75 或語義模糊不清

請以JSON格式回應：
{
  "confidence": "high 或 low",
  "reasoning": "信心評估的詳細理由"
}"""
```

#### 2.3.3 Agno Gemini LLM Interface

```python
# src/infrastructure/llm/agno_gemini_interface.py
import asyncio
from typing import Dict, Any, Optional
import google.generativeai as genai
import logging

from agno.interfaces import LLMInterface
from agno.exceptions import RetryableError, LLMError
from src.config import Settings

logger = logging.getLogger(__name__)

class AgnoGeminiInterface(LLMInterface):
    """Agno框架的Gemini LLM介面實作"""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        genai.configure(api_key=settings.gemini_api_key)
        self.model = genai.GenerativeModel("gemini-pro")
        self.rate_limiter = asyncio.Semaphore(10)  # 限制同時請求數
    
    async def generate(
        self,
        prompt: str,
        **kwargs
    ) -> str:
        """
        生成LLM回應
        
        Args:
            prompt: 輸入提示詞
            **kwargs: 額外參數 (temperature, max_tokens等)
            
        Returns:
            LLM生成的回應文本
        """
        async with self.rate_limiter:
            try:
                loop = asyncio.get_event_loop()
                
                # 配置生成參數
                generation_config = {
                    "temperature": kwargs.get("temperature", 0.1),
                    "max_output_tokens": kwargs.get("max_tokens", 1500),
                    "response_mime_type": "application/json" if kwargs.get("response_format") else None
                }
                
                # 同步轉異步執行
                def _generate():
                    response = self.model.generate_content(
                        prompt,
                        generation_config=generation_config
                    )
                    return response.text
                
                result = await loop.run_in_executor(None, _generate)
                
                logger.debug(f"Gemini回應生成成功: {len(result)} 字符")
                return result
                
            except Exception as e:
                logger.error(f"Gemini API調用失敗: {str(e)}")
                
                # 判斷是否為可重試錯誤
                if self._is_retryable_error(e):
                    raise RetryableError(f"Gemini API可重試錯誤: {str(e)}")
                else:
                    raise LLMError(f"Gemini API不可重試錯誤: {str(e)}")
    
    async def generate_structured(
        self,
        prompt: str,
        schema: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        生成結構化JSON回應
        
        Args:
            prompt: 輸入提示詞
            schema: JSON schema (可選)
            **kwargs: 額外參數
            
        Returns:
            解析後的JSON字典
        """
        import json
        
        # 強制JSON格式輸出
        kwargs["response_format"] = {"type": "json_object"}
        
        response_text = await self.generate(prompt, **kwargs)
        
        try:
            # 解析JSON回應
            result = json.loads(response_text)
            
            # 如果提供了schema，可以進行驗證
            if schema:
                self._validate_against_schema(result, schema)
            
            return result
            
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失敗: {response_text[:200]}...")
            raise LLMError(f"無效的JSON回應: {str(e)}")
        except Exception as e:
            logger.error(f"結構化回應處理失敗: {str(e)}")
            raise LLMError(f"結構化回應錯誤: {str(e)}")
    
    def _is_retryable_error(self, error: Exception) -> bool:
        """
        判斷錯誤是否可重試
        """
        error_str = str(error).lower()
        retryable_patterns = [
            "timeout",
            "rate limit",
            "service unavailable", 
            "internal server error",
            "connection error"
        ]
        
        return any(pattern in error_str for pattern in retryable_patterns)
    
    def _validate_against_schema(self, data: Dict[str, Any], schema: Dict[str, Any]):
        """
        根據schema驗證數據
        """
        # 簡單的schema驗證實作
        # 在生產環境中可能需要使用jsonschema庫
        required_fields = schema.get("required", [])
        for field in required_fields:
            if field not in data:
                raise ValueError(f"缺少必要欄位: {field}")
```

### 2.4 CLI Interface Design

#### 2.4.1 Enhanced Main CLI

```python
# src/cli/complaint_classifier.py
import asyncio
import json
import argparse
import sys
from pathlib import Path
from typing import List, Dict, Any
import logging

from src.config import Settings
from src.domain.entities.complaint_classification import ComplaintInput
from src.application.use_cases.classify_complaints import ClassifyComplaintsUseCase
from src.infrastructure.retrievers.rag_retriever import ElasticsearchRAGRetriever
from src.infrastructure.agents.complaint_classifier_agent import ComplaintClassifierAgent
from src.infrastructure.embedders.gemini_embedder import GeminiEmbedder
from src.infrastructure.repositories.elasticsearch_repository import ElasticsearchRepository

logger = logging.getLogger(__name__)

class ComplaintClassifierCLI:
    """陳情分類CLI介面"""
    
    def __init__(self):
        self.settings = Settings()
        self._setup_logging()
        self._initialize_components()
    
    def _setup_logging(self):
        """設置日誌"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(sys.stdout),
                logging.FileHandler(self.settings.log_file_path, encoding='utf-8')
            ]
        )
    
    def _initialize_components(self):
        """初始化系統組件"""
        logger.info("初始化陳情分類系統組件...")
        
        # Initialize infrastructure components
        self.embedder = GeminiEmbedder(self.settings)
        self.repository = ElasticsearchRepository(self.settings)
        
        # Initialize RAG retriever
        self.rag_retriever = ElasticsearchRAGRetriever(
            self.embedder, 
            self.repository
        )
        
        # Initialize Agno agent
        self.complaint_classifier = ComplaintClassifierAgent(self.settings)
        
        logger.info("系統組件初始化完成")
    
    def parse_arguments(self) -> argparse.Namespace:
        """解析命令列參數"""
        parser = argparse.ArgumentParser(
            description="台灣政府陳情自動分類系統",
            formatter_class=argparse.RawDescriptionHelpFormatter,
            epilog="""
使用範例:
  # 基本分類
  uv run src/main.py classify --input complaints.json
  
  # 指定輸出檔案和並發數  
  uv run src/main.py classify --input complaints.json --output results.json --concurrency 8
  
  # 詳細日誌模式
  uv run src/main.py classify --input complaints.json --verbose
  
  # 驗證輸入檔案
  uv run src/main.py classify --input complaints.json --dry-run
            """
        )
        
        parser.add_argument(
            "command",
            choices=["classify"],
            help="執行的命令"
        )
        
        parser.add_argument(
            "--input", "-i",
            required=True,
            type=Path,
            help="輸入的JSON檔案路徑"
        )
        
        parser.add_argument(
            "--output", "-o", 
            type=Path,
            help="輸出的JSON檔案路徑 (預設: 標準輸出)"
        )
        
        parser.add_argument(
            "--concurrency", "-c",
            type=int,
            default=5,
            help="並發處理數量 (預設: 5)"
        )
        
        parser.add_argument(
            "--confidence-threshold",
            choices=["high", "low"],
            default="high",
            help="信心程度閾值 (預設: high)"
        )
        
        parser.add_argument(
            "--verbose", "-v",
            action="store_true",
            help="啟用詳細日誌輸出"
        )
        
        parser.add_argument(
            "--dry-run",
            action="store_true", 
            help="僅驗證輸入檔案，不執行分類"
        )
        
        return parser.parse_args()
    
    async def run(self):
        """主要執行流程"""
        try:
            args = self.parse_arguments()
            
            if args.verbose:
                logging.getLogger().setLevel(logging.DEBUG)
            
            # Load and validate input
            complaints = self._load_complaints(args.input)
            
            if args.dry_run:
                logger.info(f"驗證完成: 共 {len(complaints)} 個陳情，格式正確")
                return
            
            # Initialize use case with concurrency setting
            use_case = ClassifyComplaintsUseCase(
                rag_retriever=self.rag_retriever,
                complaint_classifier=self.complaint_classifier,
                max_concurrency=args.concurrency
            )
            
            # Process complaints
            logger.info(f"開始處理 {len(complaints)} 個陳情 (並發數: {args.concurrency})")
            result = await use_case.process_batch(complaints)
            
            # Output results
            self._output_results(result, args.output)
            
            # Print summary
            self._print_summary(result["summary"])
            
        except Exception as e:
            logger.error(f"系統執行錯誤: {str(e)}")
            sys.exit(1)
    
    def _load_complaints(self, input_path: Path) -> List[ComplaintInput]:
        """載入陳情資料"""
        if not input_path.exists():
            raise FileNotFoundError(f"輸入檔案不存在: {input_path}")
        
        try:
            with open(input_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Validate JSON structure
            if not isinstance(data, dict) or "complaints" not in data:
                raise ValueError("JSON格式錯誤: 需要包含 'complaints' 陣列")
            
            complaints_data = data["complaints"]
            if not isinstance(complaints_data, list):
                raise ValueError("'complaints' 必須是陣列格式")
            
            # Convert to domain entities
            complaints = []
            for i, complaint_data in enumerate(complaints_data):
                try:
                    complaint = ComplaintInput(**complaint_data)
                    complaints.append(complaint)
                except Exception as e:
                    raise ValueError(f"陳情資料第 {i+1} 項格式錯誤: {str(e)}")
            
            logger.info(f"成功載入 {len(complaints)} 個陳情")
            return complaints
            
        except json.JSONDecodeError as e:
            raise ValueError(f"JSON檔案解析失敗: {str(e)}")
        except Exception as e:
            raise ValueError(f"載入檔案失敗: {str(e)}")
    
    def _output_results(self, result: Dict[str, Any], output_path: Path = None):
        """輸出結果"""
        output_data = {
            "results": [r.dict() for r in result["results"]],
            "summary": result["summary"].dict()
        }
        
        output_json = json.dumps(output_data, ensure_ascii=False, indent=2)
        
        if output_path:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(output_json)
            logger.info(f"結果已儲存至: {output_path}")
        else:
            print(output_json)
    
    def _print_summary(self, summary):
        """列印處理摘要"""
        print("\n=== 處理摘要 ===")
        print(f"總處理數量: {summary.total_processed}")
        print(f"高信心分類: {summary.high_confidence}")
        print(f"低信心分類: {summary.low_confidence}")
        print(f"處理錯誤: {summary.errors}")
        print(f"平均處理時間: {summary.average_processing_time_ms:.1f} ms")
        
        if summary.intent_distribution:
            print("\n意圖分布:")
            for intent, count in summary.intent_distribution.items():
                print(f"  {intent}: {count}")
        
        if summary.category_distribution:
            print("\n類別分布 (前10名):")
            sorted_categories = sorted(
                summary.category_distribution.items(), 
                key=lambda x: x[1], 
                reverse=True
            )
            for category, count in sorted_categories[:10]:
                print(f"  {category}: {count}")

# Enhanced main.py integration
async def main():
    """主程式入口點"""
    cli = ComplaintClassifierCLI()
    await cli.run()

if __name__ == "__main__":
    asyncio.run(main())
```

## 3. Agno Configuration Architecture

### 3.1 Agent Configuration Files

#### 3.1.1 Main Agent Configuration (YAML)

```yaml
# config/agents/complaint_classifier.yaml
agent:
  name: "complaint-classifier"
  version: "1.0.0"
  role: "台灣政府陳情分類專家"
  description: "專門處理台灣政府民眾陳情分類與意圖檢測"
  
llm_config:
  provider: "gemini"
  model: "gemini-pro"
  temperature: 0.1
  max_tokens: 1500
  response_format:
    type: "json_object"
  
templates:
  classification:
    file: "templates/classification.yaml"
    input_variables: ["complaint_text", "rag_candidates", "intent_types"]
  
  intent_detection:
    file: "templates/intent_detection.yaml" 
    input_variables: ["complaint_text", "intents_enum"]
  
  confidence_assessment:
    file: "templates/confidence.yaml"
    input_variables: ["complaint_text", "selected_category", "similarity_score"]
  
  simple_classification:
    file: "templates/simple_classification.yaml"
    input_variables: ["complaint_text", "top_candidate"]

output_schema:
  type: "json"
  schema:
    type: "object"
    required: ["main_category", "sub_category", "intents", "confidence", "reasoning"]
    properties:
      main_category:
        type: "string"
        description: "分類的主案類"
      sub_category:
        type: "string"
        description: "分類的子案類"  
      intents:
        type: "array"
        items:
          type: "string"
          enum: ["檢舉告發", "請求協助", "抱怨問題", "請求改善", "諮詢問題", "不滿服務態度", "不滿人員專業度", "感謝讚美", "提出建議", "申請服務", "要求賠償", "緊急求助", "資訊公開", "其他"]
        description: "檢測到的意圖類型列表"
      confidence:
        type: "string"
        enum: ["high", "low"]
        description: "分類信心程度"
      reasoning:
        type: "string"
        description: "分類推理說明"

retry_config:
  max_retries: 3
  backoff_strategy: "exponential"
  backoff_factor: 2.0
  retry_on_errors:
    - "timeout"
    - "rate_limit" 
    - "malformed_response"
    - "network_error"
  
fallback_config:
  enabled: true
  strategy: "simple_classification"
  fallback_threshold: 3
  default_response:
    main_category: "其他"
    sub_category: "其他"
    intents: ["其他"]
    confidence: "low"
    reasoning: "系統無法正常分類，已使用預設分類"

monitoring:
  log_level: "INFO"
  track_metrics: true
  metrics:
    - "processing_time"
    - "confidence_distribution" 
    - "error_rate"
    - "retry_count"
```

#### 3.1.2 Prompt Templates

```yaml
# templates/classification.yaml
name: "classification"
description: "主要陳情分類提示詞模板"
template: |
  你是台灣政府陳情分類專家。請根據以下資訊對民眾陳情進行精確分類：

  陳情內容：
  """
  {{ complaint_text }}
  """

  RAG檢索到的相關案類候選（按相似度排序）：
  {% if rag_candidates %}
  {{ rag_candidates }}
  {% else %}
  未找到相關候選案類，請根據陳情內容直接分類。
  {% endif %}

  分類任務：
  1. 選擇最適合的案類（主案類和子案類）
  2. 檢測所有相關的意圖類型
  3. 評估分類信心程度（high/low）
  4. 提供詳細的分類理由

  可選擇的意圖類型：
  {% for intent in intent_types %}
  - {{ intent }}
  {% endfor %}

  分類標準：
  - 優先選擇相似度最高且語義最匹配的案類
  - 可以檢測多個意圖，無需排序或優先級
  - 相似度 >= 0.75 且語義明確匹配 → confidence = "high"
  - 相似度 < 0.75 或語義模糊 → confidence = "low" 
  - 如果沒有合適的候選案類，分類至"其他"

  請以標準JSON格式回應：
  {
    "main_category": "選擇的主案類名稱",
    "sub_category": "選擇的子案類名稱",
    "intents": ["檢測到的所有意圖"],
    "confidence": "high或low",
    "reasoning": "詳細說明選擇此分類的理由，包括考慮的因素和決策過程"
  }

input_variables:
  - complaint_text
  - rag_candidates  
  - intent_types

validation:
  required_fields: ["main_category", "sub_category", "intents", "confidence", "reasoning"]
  confidence_values: ["high", "low"]
```

```yaml
# templates/simple_classification.yaml  
name: "simple_classification"
description: "簡化分類模板，用於降級處理"
template: |
  請對以下陳情進行基本分類：

  陳情內容：
  """
  {{ complaint_text }}
  """
  
  {% if top_candidate %}
  推薦案類：
  主案類：{{ top_candidate.main_category }}
  子案類：{{ top_candidate.sub_category }}
  描述：{{ top_candidate.sub_description }}
  {% endif %}

  請判斷此陳情最適合的分類，並以JSON格式回應：
  {
    "main_category": "主案類",
    "sub_category": "子案類",
    "intents": ["基本意圖"],
    "confidence": "low",
    "reasoning": "簡要說明"
  }

input_variables:
  - complaint_text
  - top_candidate
```

### 3.2 Performance Monitoring & Metrics

#### 3.2.1 Metrics Collection Design

```python
# src/infrastructure/monitoring/metrics_collector.py
from typing import Dict, List, Any
from datetime import datetime, timedelta
import asyncio
from collections import defaultdict, deque
import logging

logger = logging.getLogger(__name__)

class ClassificationMetricsCollector:
    """分類指標收集器"""
    
    def __init__(self, window_size_minutes: int = 60):
        self.window_size = timedelta(minutes=window_size_minutes)
        self.metrics_buffer = deque()
        self.aggregated_metrics = defaultdict(list)
        self._lock = asyncio.Lock()
    
    async def record_classification(
        self,
        complaint_id: str,
        processing_time_ms: int,
        confidence: str,
        similarity_score: float,
        success: bool,
        error_type: str = None
    ):
        """記錄分類指標"""
        async with self._lock:
            timestamp = datetime.now()
            
            metric_record = {
                'timestamp': timestamp,
                'complaint_id': complaint_id,
                'processing_time_ms': processing_time_ms,
                'confidence': confidence,
                'similarity_score': similarity_score,
                'success': success,
                'error_type': error_type
            }
            
            self.metrics_buffer.append(metric_record)
            
            # 清理過期數據
            cutoff_time = timestamp - self.window_size
            while self.metrics_buffer and self.metrics_buffer[0]['timestamp'] < cutoff_time:
                self.metrics_buffer.popleft()
    
    async def get_performance_summary(self) -> Dict[str, Any]:
        """獲取效能摘要"""
        async with self._lock:
            if not self.metrics_buffer:
                return {}
            
            # 計算各項指標
            total_requests = len(self.metrics_buffer)
            successful_requests = sum(1 for m in self.metrics_buffer if m['success'])
            failed_requests = total_requests - successful_requests
            
            processing_times = [m['processing_time_ms'] for m in self.metrics_buffer if m['success']]
            avg_processing_time = sum(processing_times) / len(processing_times) if processing_times else 0
            
            confidence_dist = defaultdict(int)
            for m in self.metrics_buffer:
                if m['success']:
                    confidence_dist[m['confidence']] += 1
            
            error_dist = defaultdict(int)
            for m in self.metrics_buffer:
                if not m['success'] and m['error_type']:
                    error_dist[m['error_type']] += 1
            
            return {
                'total_requests': total_requests,
                'successful_requests': successful_requests,
                'failed_requests': failed_requests,
                'success_rate': successful_requests / total_requests if total_requests > 0 else 0,
                'avg_processing_time_ms': avg_processing_time,
                'confidence_distribution': dict(confidence_dist),
                'error_distribution': dict(error_dist),
                'window_size_minutes': self.window_size.total_seconds() / 60
            }
```

## 4. Error Handling & Recovery Strategy

### 4.1 Comprehensive Error Handling Architecture

```mermaid
graph TD
    A[Request] --> B{RAG Stage}
    B -->|Success| C{Agno Agent Stage}
    B -->|Embedding Error| D[Embedding Retry]
    B -->|ES Connection Error| E[ES Retry]
    
    D -->|Retry Success| C
    D -->|Retry Failed| F[Keyword Fallback]
    E -->|Retry Success| C
    E -->|Retry Failed| F
    
    C -->|Success| G[Success Response]
    C -->|LLM Error| H[LLM Retry]
    C -->|Parse Error| I[Parse Retry]
    
    H -->|Retry Success| G
    H -->|Retry Failed| J[Simple Template Fallback]
    I -->|Retry Success| G  
    I -->|Retry Failed| J
    
    J -->|Success| K[Degraded Response]
    J -->|Failed| L[Default Response]
    
    F --> M[Basic Classification]
    M --> K
```

### 4.2 Error Categories and Handling Strategies

```python
# src/infrastructure/error_handling/classification_errors.py
from enum import Enum
from typing import Optional, Dict, Any
from dataclasses import dataclass

class ErrorCategory(Enum):
    """錯誤類別枚舉"""
    # RAG Stage Errors
    EMBEDDING_ERROR = "embedding_error"
    ELASTICSEARCH_ERROR = "elasticsearch_error"
    RAG_NO_CANDIDATES = "rag_no_candidates"
    
    # LLM Stage Errors  
    LLM_TIMEOUT = "llm_timeout"
    LLM_RATE_LIMIT = "llm_rate_limit"
    LLM_MALFORMED_RESPONSE = "llm_malformed_response"
    LLM_API_ERROR = "llm_api_error"
    
    # System Errors
    VALIDATION_ERROR = "validation_error"
    CONFIGURATION_ERROR = "configuration_error"
    MEMORY_ERROR = "memory_error"
    
    # Input Errors
    INVALID_INPUT = "invalid_input"
    EMPTY_CONTENT = "empty_content"
    CONTENT_TOO_LONG = "content_too_long"

@dataclass
class ErrorContext:
    """錯誤上下文資訊"""
    category: ErrorCategory
    message: str
    complaint_id: Optional[str] = None
    retry_attempt: int = 0
    max_retries: int = 3
    recoverable: bool = True
    fallback_applied: bool = False
    additional_info: Optional[Dict[str, Any]] = None

class ErrorHandler:
    """錯誤處理器"""
    
    def __init__(self):
        self.error_strategies = {
            ErrorCategory.EMBEDDING_ERROR: self._handle_embedding_error,
            ErrorCategory.ELASTICSEARCH_ERROR: self._handle_elasticsearch_error,
            ErrorCategory.LLM_TIMEOUT: self._handle_llm_timeout,
            ErrorCategory.LLM_RATE_LIMIT: self._handle_rate_limit,
            ErrorCategory.LLM_MALFORMED_RESPONSE: self._handle_malformed_response,
            ErrorCategory.RAG_NO_CANDIDATES: self._handle_no_candidates,
            ErrorCategory.VALIDATION_ERROR: self._handle_validation_error,
            ErrorCategory.INVALID_INPUT: self._handle_invalid_input,
        }
    
    async def handle_error(self, error_context: ErrorContext) -> Dict[str, Any]:
        """
        處理錯誤並返回恢復策略
        
        Returns:
            包含恢復行動的字典
        """
        handler = self.error_strategies.get(
            error_context.category, 
            self._handle_unknown_error
        )
        
        return await handler(error_context)
    
    async def _handle_embedding_error(self, ctx: ErrorContext) -> Dict[str, Any]:
        """處理嵌入生成錯誤"""
        if ctx.retry_attempt < ctx.max_retries:
            return {
                "action": "retry",
                "delay_seconds": 2 ** ctx.retry_attempt,
                "strategy": "exponential_backoff"
            }
        else:
            return {
                "action": "fallback", 
                "fallback_type": "keyword_matching",
                "reason": "嵌入生成失敗，改用關鍵詞匹配"
            }
    
    async def _handle_elasticsearch_error(self, ctx: ErrorContext) -> Dict[str, Any]:
        """處理Elasticsearch錯誤"""
        if "connection" in ctx.message.lower():
            return {
                "action": "retry",
                "delay_seconds": 5,
                "max_retries": 2
            }
        else:
            return {
                "action": "fallback",
                "fallback_type": "direct_llm_classification", 
                "reason": "Elasticsearch查詢失敗，直接使用LLM分類"
            }
    
    async def _handle_llm_timeout(self, ctx: ErrorContext) -> Dict[str, Any]:
        """處理LLM超時錯誤"""
        return {
            "action": "retry",
            "delay_seconds": 3,
            "timeout_increase": 1.5,  # 增加超時時間
            "max_retries": 2
        }
    
    async def _handle_rate_limit(self, ctx: ErrorContext) -> Dict[str, Any]:
        """處理速率限制錯誤"""
        return {
            "action": "retry",
            "delay_seconds": 60,  # 等待1分鐘
            "backoff_strategy": "fixed",
            "max_retries": 1
        }
    
    async def _handle_malformed_response(self, ctx: ErrorContext) -> Dict[str, Any]:
        """處理格式錯誤的回應"""
        if ctx.retry_attempt < 2:
            return {
                "action": "retry",
                "template": "simple_classification",  # 使用簡化模板
                "additional_instructions": "請確保回應為有效的JSON格式"
            }
        else:
            return {
                "action": "fallback",
                "fallback_type": "default_classification",
                "reason": "無法獲得有效的LLM回應"
            }
    
    async def _handle_no_candidates(self, ctx: ErrorContext) -> Dict[str, Any]:
        """處理無候選結果錯誤"""
        return {
            "action": "fallback",
            "fallback_type": "direct_llm_classification",
            "reason": "RAG檢索無結果，直接使用LLM分類",
            "lower_threshold": True
        }
    
    async def _handle_validation_error(self, ctx: ErrorContext) -> Dict[str, Any]:
        """處理驗證錯誤"""
        return {
            "action": "reject",
            "reason": "輸入資料驗證失敗",
            "user_action_required": True
        }
    
    async def _handle_invalid_input(self, ctx: ErrorContext) -> Dict[str, Any]:
        """處理無效輸入錯誤"""
        return {
            "action": "reject", 
            "reason": "輸入內容無效或格式錯誤",
            "user_action_required": True
        }
    
    async def _handle_unknown_error(self, ctx: ErrorContext) -> Dict[str, Any]:
        """處理未知錯誤"""
        return {
            "action": "fallback",
            "fallback_type": "default_classification",
            "reason": f"未知錯誤: {ctx.message}"
        }
```

## 5. Memory Management & Batch Processing

### 5.1 Memory-Efficient Batch Processing

```python
# src/infrastructure/batch_processing/memory_manager.py
import asyncio
import gc
from typing import List, AsyncIterator, TypeVar, Generic
from dataclasses import dataclass
import psutil
import logging

logger = logging.getLogger(__name__)

T = TypeVar('T')

@dataclass
class MemoryConfig:
    """記憶體配置參數"""
    max_memory_usage_mb: int = 1024  # 最大記憶體使用量(MB)
    batch_size: int = 10  # 每批次處理數量
    gc_threshold: int = 50  # 垃圾回收閾值(MB)
    memory_check_interval: int = 5  # 記憶體檢查間隔(處理數量)

class MemoryAwareBatchProcessor(Generic[T]):
    """記憶體感知的批次處理器"""
    
    def __init__(self, config: MemoryConfig):
        self.config = config
        self.processed_count = 0
        self.initial_memory = self._get_memory_usage()
    
    async def process_in_batches(
        self,
        items: List[T],
        processor_func,
        *args,
        **kwargs
    ) -> List:
        """
        分批處理項目，自動管理記憶體
        """
        results = []
        current_batch = []
        
        logger.info(f"開始批次處理: {len(items)} 項目, 批次大小: {self.config.batch_size}")
        
        for i, item in enumerate(items):
            current_batch.append(item)
            
            # 當達到批次大小或是最後一批時處理
            if len(current_batch) >= self.config.batch_size or i == len(items) - 1:
                # 處理當前批次
                batch_results = await self._process_batch(
                    current_batch, processor_func, *args, **kwargs
                )
                results.extend(batch_results)
                
                # 清理批次資料
                current_batch.clear()
                
                # 記憶體管理
                await self._manage_memory()
                
                logger.info(f"已處理: {len(results)}/{len(items)} ({len(results)/len(items)*100:.1f}%)")
        
        logger.info(f"批次處理完成，總計處理: {len(results)} 項目")
        return results
    
    async def _process_batch(self, batch: List[T], processor_func, *args, **kwargs) -> List:
        """處理單一批次"""
        try:
            # 為批次中的每個項目創建任務
            tasks = [
                processor_func(item, *args, **kwargs) 
                for item in batch
            ]
            
            # 並發處理批次內的所有項目
            batch_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 過濾異常結果
            valid_results = []
            for result in batch_results:
                if not isinstance(result, Exception):
                    valid_results.append(result)
                else:
                    logger.error(f"批次處理項目失敗: {str(result)}")
            
            return valid_results
            
        except Exception as e:
            logger.error(f"批次處理失敗: {str(e)}")
            return []
    
    async def _manage_memory(self):
        """記憶體管理"""
        self.processed_count += 1
        
        # 定期檢查記憶體使用量
        if self.processed_count % self.config.memory_check_interval == 0:
            current_memory = self._get_memory_usage()
            memory_increase = current_memory - self.initial_memory
            
            logger.debug(f"記憶體使用量: {current_memory:.1f}MB (+{memory_increase:.1f}MB)")
            
            # 如果記憶體增長超過閾值，觸發垃圾回收
            if memory_increase > self.config.gc_threshold:
                logger.info("觸發垃圾回收...")
                gc.collect()
                
                # 更新基準記憶體使用量
                self.initial_memory = self._get_memory_usage()
                logger.info(f"垃圾回收完成，當前記憶體: {self.initial_memory:.1f}MB")
            
            # 如果超過最大記憶體限制，暫停處理
            if current_memory > self.config.max_memory_usage_mb:
                logger.warning(f"記憶體使用量超限({current_memory:.1f}MB > {self.config.max_memory_usage_mb}MB)")
                await asyncio.sleep(5)  # 暫停5秒讓系統恢復
                gc.collect()
    
    def _get_memory_usage(self) -> float:
        """獲取當前記憶體使用量(MB)"""
        process = psutil.Process()
        return process.memory_info().rss / 1024 / 1024

class StreamingBatchProcessor:
    """流式批次處理器，適用於大檔案"""
    
    async def process_stream(
        self,
        input_stream: AsyncIterator[T],
        processor_func,
        batch_size: int = 10
    ) -> AsyncIterator[List]:
        """
        流式處理輸入
        """
        batch = []
        
        async for item in input_stream:
            batch.append(item)
            
            if len(batch) >= batch_size:
                results = await self._process_batch(batch, processor_func)
                yield results
                batch.clear()
                
                # 主動觸發垃圾回收
                gc.collect()
        
        # 處理最後一批
        if batch:
            results = await self._process_batch(batch, processor_func)
            yield results
    
    async def _process_batch(self, batch: List[T], processor_func) -> List:
        """處理批次"""
        tasks = [processor_func(item) for item in batch]
        return await asyncio.gather(*tasks, return_exceptions=True)
```

## 6. Configuration Management

### 6.1 Enhanced Settings for Agno Integration

```python
# src/config/settings.py (Enhanced)
from pydantic import BaseSettings, Field, validator
from typing import Optional, Dict, Any, List
from pathlib import Path
import os

class AgnoSettings(BaseSettings):
    """Agno框架專用配置"""
    
    # Agno Agent Configuration
    agent_config_path: Path = Field(
        default=Path("config/agents/complaint_classifier.yaml"),
        description="Agno代理配置檔案路徑"
    )
    
    template_dir: Path = Field(
        default=Path("templates"),
        description="提示詞模板目錄"
    )
    
    # LLM Configuration
    llm_temperature: float = Field(default=0.1, ge=0.0, le=2.0)
    llm_max_tokens: int = Field(default=1500, gt=0)
    llm_timeout_seconds: int = Field(default=30, gt=0)
    
    # Retry Configuration
    max_retries: int = Field(default=3, ge=0, le=10)
    retry_backoff_factor: float = Field(default=2.0, gt=1.0)
    retry_timeout_increase: float = Field(default=1.5, gt=1.0)
    
    # Fallback Configuration
    enable_fallback: bool = Field(default=True)
    fallback_threshold: int = Field(default=3, gt=0)
    
    # Monitoring
    enable_metrics: bool = Field(default=True)
    metrics_window_minutes: int = Field(default=60, gt=0)

class ClassificationSettings(BaseSettings):
    """分類專用配置"""
    
    # RAG Configuration
    rag_similarity_threshold: float = Field(default=0.6, ge=0.0, le=1.0)
    rag_fallback_threshold: float = Field(default=0.4, ge=0.0, le=1.0)
    rag_candidate_limit: int = Field(default=10, gt=0)
    
    # Confidence Thresholds
    high_confidence_threshold: float = Field(default=0.75, ge=0.0, le=1.0)
    
    # Processing Configuration
    default_concurrency: int = Field(default=5, gt=0, le=20)
    max_batch_size: int = Field(default=500, gt=0)
    processing_timeout_ms: int = Field(default=2000, gt=0)
    
    # Memory Management
    memory_limit_mb: int = Field(default=1024, gt=0)
    gc_threshold_mb: int = Field(default=50, gt=0)
    
    # Intent Types
    intent_types: List[str] = Field(
        default=[
            "檢舉告發", "請求協助", "抱怨問題", "請求改善", "諮詢問題",
            "不滿服務態度", "不滿人員專業度", "感謝讚美", "提出建議", 
            "申請服務", "要求賠償", "緊急求助", "資訊公開", "其他"
        ]
    )

class EnhancedSettings(BaseSettings):
    """增強的系統配置"""
    
    # Existing settings (from original Settings class)
    environment: str = Field(default="local")
    
    # Gemini Configuration  
    gemini_api_key: str = Field(..., description="Google Gemini API密鑰")
    embedding_dims: int = Field(default=3072)
    embedding_task_type: str = Field(default="RETRIEVAL_DOCUMENT")
    
    # Elasticsearch Configuration
    elasticsearch_url: str = Field(default="http://localhost:9200")
    elasticsearch_username: Optional[str] = Field(default=None)
    elasticsearch_password: Optional[str] = Field(default=None)
    main_categories_index: str = Field(default="main_categories")
    sub_categories_index: str = Field(default="sub_categories")
    
    # New Configuration Sections
    agno: AgnoSettings = Field(default_factory=AgnoSettings)
    classification: ClassificationSettings = Field(default_factory=ClassificationSettings)
    
    # Logging Configuration
    log_file_path: Path = Field(default=Path("src/app.log"))
    log_level: str = Field(default="INFO")
    log_format: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        env_nested_delimiter = "__"  # 支持巢狀配置 (e.g., AGNO__MAX_RETRIES)
        
    @validator('gemini_api_key')
    def validate_gemini_key(cls, v):
        if not v or len(v) < 10:
            raise ValueError("Gemini API密鑰無效")
        return v
        
    @validator('elasticsearch_url')
    def validate_elasticsearch_url(cls, v):
        if not v.startswith(('http://', 'https://')):
            raise ValueError("Elasticsearch URL必須以http://或https://開頭")
        return v
```

## 7. Integration Testing Strategy

### 7.1 End-to-End Test Architecture

```python
# tests/integration/test_classification_pipeline.py
import pytest
import asyncio
from pathlib import Path
from typing import List

from src.domain.entities.complaint_classification import ComplaintInput, ConfidenceLevel
from src.application.use_cases.classify_complaints import ClassifyComplaintsUseCase
from src.infrastructure.retrievers.rag_retriever import ElasticsearchRAGRetriever
from src.infrastructure.agents.complaint_classifier_agent import ComplaintClassifierAgent

@pytest.mark.asyncio
class TestClassificationPipeline:
    """端到端分類流程測試"""
    
    @pytest.fixture
    async def classification_system(self, test_settings):
        """初始化完整的分類系統"""
        # Mock or use test instances of dependencies
        embedder = MockGeminiEmbedder()
        repository = MockElasticsearchRepository()
        rag_retriever = ElasticsearchRAGRetriever(embedder, repository)
        agent = ComplaintClassifierAgent(test_settings)
        
        use_case = ClassifyComplaintsUseCase(
            rag_retriever=rag_retriever,
            complaint_classifier=agent,
            max_concurrency=3
        )
        
        return use_case
    
    @pytest.fixture
    def sample_complaints(self) -> List[ComplaintInput]:
        """測試用陳情資料"""
        return [
            ComplaintInput(
                id="test_001",
                content="我要檢舉某建築工地違法施工，晚上施工噪音很大影響居民休息"
            ),
            ComplaintInput(
                id="test_002", 
                content="請協助處理我的身分證申請案件，已經等待兩個月了"
            ),
            ComplaintInput(
                id="test_003",
                content="對於市政府人員的服務態度非常不滿，態度很差又沒有專業知識"
            )
        ]
    
    async def test_complete_pipeline(self, classification_system, sample_complaints):
        """測試完整的分類流程"""
        results = await classification_system.process_batch(sample_complaints)
        
        # 驗證結果結構
        assert "results" in results
        assert "summary" in results
        
        classification_results = results["results"]
        assert len(classification_results) == len(sample_complaints)
        
        # 驗證每個結果
        for result in classification_results:
            assert result.complaint_id in [c.id for c in sample_complaints]
            assert result.main_category is not None
            assert result.sub_category is not None
            assert len(result.intents) > 0
            assert result.confidence in [ConfidenceLevel.HIGH, ConfidenceLevel.LOW]
            assert result.processing_time_ms > 0
    
    async def test_performance_requirements(self, classification_system, sample_complaints):
        """測試效能要求"""
        import time
        
        start_time = time.time()
        results = await classification_system.process_batch(sample_complaints)
        total_time = time.time() - start_time
        
        # 驗證總處理時間
        assert total_time < 10.0  # 3個陳情應該在10秒內完成
        
        # 驗證個別處理時間
        for result in results["results"]:
            assert result.processing_time_ms < 2000  # <2秒要求
    
    async def test_error_handling(self, classification_system):
        """測試錯誤處理"""
        # 測試空內容
        invalid_complaint = ComplaintInput(id="invalid", content="")
        
        with pytest.raises(ValueError):
            await classification_system.process_single_complaint(invalid_complaint)
        
        # 測試短內容
        short_complaint = ComplaintInput(id="short", content="太短")
        result = await classification_system.process_single_complaint(short_complaint)
        
        assert result.confidence == ConfidenceLevel.LOW
        assert "內容過短" in result.reasoning or result.main_category == "其他"
    
    async def test_concurrent_processing(self, classification_system):
        """測試並發處理"""
        # 創建大批陳情
        complaints = [
            ComplaintInput(id=f"concurrent_{i}", content=f"測試陳情內容 {i}")
            for i in range(20)
        ]
        
        results = await classification_system.process_batch(complaints)
        
        # 驗證所有陳情都被處理
        assert len(results["results"]) == len(complaints)
        
        # 驗證並發處理沒有產生重複或遺失
        result_ids = {r.complaint_id for r in results["results"]}
        input_ids = {c.id for c in complaints}
        assert result_ids == input_ids

    @pytest.mark.parametrize("intent_type,expected_keywords", [
        ("檢舉告發", ["違法", "檢舉", "舉報"]),
        ("請求協助", ["協助", "幫忙", "申請"]), 
        ("抱怨問題", ["不滿", "抱怨", "問題"]),
        ("感謝讚美", ["感謝", "讚美", "很好"])
    ])
    async def test_intent_detection_accuracy(
        self, 
        classification_system, 
        intent_type, 
        expected_keywords
    ):
        """測試意圖檢測準確性"""
        # 為每種意圖類型創建包含相關關鍵詞的陳情
        content = f"這是一個包含 {' '.join(expected_keywords)} 的測試陳情內容"
        complaint = ComplaintInput(id=f"intent_test", content=content)
        
        result = await classification_system.process_single_complaint(complaint)
        
        # 驗證是否檢測到預期的意圖類型
        detected_intents = [intent.value for intent in result.intents]
        assert intent_type in detected_intents or "其他" in detected_intents
```

## 8. Deployment Considerations

### 8.1 Production Deployment Architecture

```yaml
# docker/Dockerfile
FROM python:3.12-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Install uv
RUN pip install uv

# Copy project files
COPY pyproject.toml uv.lock ./
RUN uv sync --frozen

# Copy application code
COPY src/ ./src/
COPY config/ ./config/
COPY templates/ ./templates/

# Create non-root user
RUN useradd -m -u 1001 appuser && chown -R appuser:appuser /app
USER appuser

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# Default command
CMD ["uv", "run", "src/main.py", "--help"]
```

```yaml
# docker-compose.yml
version: '3.8'

services:
  complaint-classifier:
    build: .
    environment:
      - ENVIRONMENT=prod
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - ELASTICSEARCH_URL=http://elasticsearch:9200
      - AGNO__MAX_RETRIES=3
      - CLASSIFICATION__DEFAULT_CONCURRENCY=10
    volumes:
      - ./data:/app/data:ro
      - ./results:/app/results
      - ./logs:/app/logs
    depends_on:
      - elasticsearch
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '1.0'
          memory: 1G

  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.13.2
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
    ports:
      - "9200:9200"
    volumes:
      - es_data:/usr/share/elasticsearch/data

volumes:
  es_data:
```

This comprehensive design document provides a complete architecture for implementing the Complaint Classification Agent with full Agno framework integration. The design includes detailed component specifications, error handling strategies, memory management, and production deployment considerations. All components are designed to work seamlessly with the existing RAG infrastructure while providing the advanced capabilities required for accurate complaint classification and multi-intent detection.

The implementation follows Clean Architecture principles and provides clear interfaces for future extensibility, including REST API and web UI development. The Agno framework integration ensures robust error handling, retry mechanisms, and structured output generation while maintaining high performance and reliability standards.
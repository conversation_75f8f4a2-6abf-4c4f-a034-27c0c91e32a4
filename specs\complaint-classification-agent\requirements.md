# Complaint Classification Agent - Technical Requirements

## 1. Overview

This specification defines the technical requirements for implementing a Complaint Classification Agent that uses a **RAG + Agno Agent + LLM dual-stage classification workflow** to automatically classify Taiwan government citizen complaints into appropriate categories while analyzing their underlying intents.

## 2. Functional Requirements

### 2.1 Core Classification Engine (RAG + Agno + LLM)

#### FR-001: RAG Vector Retrieval Stage

**WHEN** the system receives a complaint text
**THEN** it MUST generate embeddings using Gemini embedding-001 with task_type="RETRIEVAL_QUERY"
**AND** it MUST query Elasticsearch sub_categories index for top 10 most similar candidates using cosine similarity
**AND** it MUST filter candidates with similarity score >= 0.75
**AND** the RAG retrieval process MUST complete within 500ms

#### FR-002: Agno Agent Classification Stage

**WHEN** the system receives RAG candidate categories
**THEN** it MUST instantiate an Agno complaint classification agent
**AND** it MUST use Agno framework to call Gemini Chat API for final classification decision
**AND** it MUST utilize <PERSON>gno's prompt template management for classification prompts
**AND** it MUST return structured JSON output using <PERSON>gno's structured output capabilities
**AND** the LLM classification process MUST complete within 3000ms

#### FR-003: Multi-Intent Detection

**WHEN** the system processes a complaint
**THEN** it MUST detect all relevant intents from the complete 14-type enum:

1. **檢舉告發** - 舉報違法違規行為
2. **請求協助** - 需要政府單位幫忙解決問題
3. **抱怨問題** - 對現況表達不滿
4. **請求改善** - 要求改善現有狀況或制度
5. **諮詢問題** - 詢問相關資訊或程序
6. **不滿服務態度** - 對公務人員服務態度不滿
7. **不滿人員專業度** - 對公務人員專業能力不滿
8. **感謝讚美** - 表達感謝或讚美
9. **提出建議** - 提供改善建議或意見
10. **申請服務** - 申請各種公共服務或證件
11. **要求賠償** - 因政府行為造成損失而要求賠償
12. **緊急求助** - 需要立即處理的緊急狀況
13. **資訊公開** - 要求公開政府資訊或資料
14. **其他** - 無法歸類至上述任何類別的意圖

**AND** it MUST support detection of multiple intents per complaint without prioritization
**AND** it MUST provide confidence assessment for each detected intent

#### FR-004: Confidence Assessment

**WHEN** the Agno agent completes classification
**THEN** it MUST self-evaluate confidence using only two levels: "high" or "low"
**AND** confidence assessment MUST consider:

-   RAG candidate similarity scores
-   LLM classification certainty
-   Complaint text clarity and completeness
-   Consistency between multiple potential categories

**AND** high confidence threshold MUST be >= 0.8 overall assessment
**AND** low confidence MUST trigger manual review recommendations

### 2.2 Agno Framework Integration

#### FR-005: Agent Architecture

**WHEN** initializing the classification system
**THEN** it MUST create an Agno complaint classification agent with defined schema
**AND** it MUST configure agent with specific role: "Taiwan government complaint classifier"
**AND** it MUST integrate with Gemini Chat API through Agno's LLM interface
**AND** it MUST support agent context preservation across classification requests

#### FR-006: Prompt Template Management

**WHEN** the agent needs to classify complaints
**THEN** it MUST use Agno's prompt template system for:

-   Category classification prompt with RAG candidates
-   Multi-intent detection prompt with all 14 intent types
-   Confidence assessment prompt with criteria definition
-   Edge case handling prompts (short text, unclear content, etc.)

**AND** all prompts MUST be externally configurable through Agno templates
**AND** prompts MUST include Traditional Chinese language instructions
**AND** prompts MUST incorporate RAG retrieval context effectively

#### FR-007: Structured Output Schema

**WHEN** the Agno agent processes classification
**THEN** it MUST enforce structured JSON output with exact schema:

```json
{
	"complaint_id": "string",
	"main_category": "string",
	"sub_category": "string",
	"intents": ["array of intent enums"],
	"confidence": "high|low",
	"similarity_score": "float",
	"reasoning": "string explanation",
	"processing_time_ms": "integer",
	"rag_candidates": ["array of top candidates"]
}
```

**AND** it MUST validate output against Pydantic schema
**AND** it MUST reject malformed responses and retry with Agno's retry mechanism

### 2.3 CLI Interface & Batch Processing

#### FR-008: Command Line Interface

**WHEN** users invoke the CLI tool
**THEN** it MUST accept JSON batch files with complaint arrays
**AND** it MUST support command options:

-   `--input <file_path>`: Input JSON file path
-   `--output <file_path>`: Output JSON file path (optional, defaults to stdout)
-   `--concurrency <number>`: Concurrent processing limit (default: 5)
-   `--confidence-threshold <high|low>`: Minimum confidence for auto-acceptance
-   `--verbose`: Enable detailed logging

**AND** it MUST validate input JSON format before processing
**AND** it MUST provide progress indicators for batch processing

#### FR-009: Concurrent Processing

**WHEN** processing multiple complaints
**THEN** it MUST support configurable concurrent processing (default: 5 concurrent)
**AND** it MUST implement memory management to prevent OOM with large batches
**AND** each complaint MUST be processed independently without state dependencies
**AND** it MUST collect and aggregate processing metrics across concurrent tasks

#### FR-010: Input Format Validation

**WHEN** receiving input JSON
**THEN** it MUST validate required structure:

```json
{
	"complaints": [
		{
			"id": "string (required)",
			"content": "string (required, min 10 chars)",
			"metadata": "object (optional)"
		}
	]
}
```

**AND** it MUST reject files with malformed structure
**AND** it MUST provide specific validation error messages

### 2.4 Error Handling & Edge Cases

#### FR-011: Agno Error Handling Integration

**WHEN** encountering processing errors
**THEN** it MUST utilize Agno's built-in retry mechanism for:

-   LLM API timeouts (max 3 retries)
-   Network connectivity issues (max 2 retries)
-   Malformed LLM responses (max 2 retries with schema correction)

**AND** it MUST implement Agno fallback strategies:

-   Fallback to simpler prompts on complex failures
-   Graceful degradation to keyword-based classification
-   Default to "其他" category with low confidence on complete failure

#### FR-012: Edge Case Processing

**WHEN** encountering edge cases
**THEN** the system MUST handle:

-   **Short complaints** (< 10 characters): Flag as insufficient content, attempt basic classification
-   **Non-Chinese text**: Detect language mismatch, attempt translation or flag for manual review
-   **Empty/whitespace-only content**: Return validation error immediately
-   **Extremely long content** (> 5000 characters): Truncate to first 5000 chars with warning

**AND** all edge cases MUST be logged with specific categorization
**AND** edge cases MUST default to low confidence classification

#### FR-013: RAG Retrieval Fallbacks

**WHEN** RAG retrieval fails or returns no candidates
**THEN** the system MUST implement fallback strategy:

-   Lower similarity threshold from 0.6 to 0.4
-   If still no candidates, use keyword matching on complaint text
-   If keyword matching fails, use Agno agent for direct classification without RAG context
-   Final fallback: assign to "其他" category with low confidence

## 3. Non-Functional Requirements

### 3.1 Performance Requirements

#### NFR-001: End-to-End Processing Time

**GIVEN** a single complaint processing request
**WHEN** the system processes through RAG + Agno + LLM workflow
**THEN** total processing time MUST be < 2000ms (2 seconds)
**INCLUDING** embedding generation (500ms) + RAG search (200ms) + Agno LLM processing (1200ms) + overhead (100ms)

#### NFR-002: Concurrent Processing Performance

**GIVEN** batch processing with 5 concurrent complaints
**WHEN** processing multiple complaints simultaneously
**THEN** each complaint MUST still meet <2 second individual processing requirement
**AND** memory usage MUST remain below 1GB for batches up to 100 complaints
**AND** CPU usage MUST not exceed 80% during peak processing

#### NFR-003: Accuracy Requirements

**GIVEN** manual validation dataset
**WHEN** comparing system classifications to human expert classifications
**THEN** overall accuracy MUST be >= 90%
**AND** high confidence classifications MUST achieve >= 95% accuracy
**AND** low confidence classifications MUST achieve >= 70% accuracy
**AND** multi-intent detection MUST achieve >= 85% precision and recall

### 3.2 Reliability & Error Handling

#### NFR-004: System Availability

**GIVEN** normal operating conditions
**WHEN** processing complaint batches
**THEN** system error rate MUST be < 5%
**AND** transient errors MUST be handled with appropriate retry mechanisms
**AND** permanent failures MUST be logged with actionable error messages

#### NFR-005: Memory Management

**GIVEN** batch processing scenarios
**WHEN** processing files with 50+ complaints
**THEN** memory usage MUST not grow linearly with batch size
**AND** garbage collection MUST be triggered appropriately
**AND** system MUST process batches of up to 500 complaints without memory errors

### 3.3 Usability & Maintainability

#### NFR-006: Configuration Management

**GIVEN** deployment environments
**WHEN** configuring the classification agent
**THEN** all Agno agent parameters MUST be externally configurable
**AND** prompt templates MUST be modifiable without code changes
**AND** confidence thresholds MUST be adjustable via configuration
**AND** concurrent processing limits MUST be environment-specific

#### NFR-007: Logging & Observability

**GIVEN** production operation
**WHEN** processing complaints
**THEN** the system MUST log:

-   Processing metrics (time, confidence distribution, error rates)
-   Classification patterns and category frequency
-   Agno agent decision reasoning
-   Performance bottlenecks and optimization opportunities

**AND** logs MUST be structured (JSON format) for analysis
**AND** sensitive complaint content MUST be redacted in logs

## 4. Technical Constraints

### 4.1 Infrastructure Integration

#### TC-001: Existing Infrastructure Reuse

**CONSTRAINT:** MUST utilize existing RAG infrastructure without modification:

-   GeminiEmbedder for embedding generation
-   ElasticsearchRepository for vector search
-   Existing main_categories and sub_categories indices
-   Current Clean Architecture patterns and layer structure

#### TC-002: Agno Framework Dependency

**CONSTRAINT:** MUST integrate Agno framework as primary agent orchestration system:

-   Add agno-framework to pyproject.toml dependencies
-   Follow Agno patterns for agent definition and configuration
-   Use Agno's LLM interface rather than direct Gemini Chat API integration
-   Implement Agno-compatible error handling and retry logic

### 4.2 Platform & Technology Constraints

#### TC-003: Python Environment

**CONSTRAINT:** MUST maintain Python 3.12 compatibility
**AND** MUST use uv package manager for dependency management
**AND** MUST follow existing async/await patterns
**AND** MUST maintain Windows platform compatibility

#### TC-004: API Rate Limits

**CONSTRAINT:** MUST respect Gemini API rate limits:

-   Embedding API: existing rate limiting in GeminiEmbedder
-   Chat API: implement new rate limiting for Agno agent LLM calls
-   Concurrent request limits: max 10 simultaneous API calls
-   Daily quota management with graceful degradation

### 4.3 Data & Security Constraints

#### TC-005: Data Privacy

**CONSTRAINT:** MUST NOT persist complaint content in any storage system
**AND** MUST redact personal information in logs and outputs
**AND** MUST process data in-memory only with proper cleanup
**AND** MUST comply with Taiwan government data handling requirements

#### TC-006: Traditional Chinese Language Focus

**CONSTRAINT:** MUST optimize for Traditional Chinese text processing only
**AND** MUST handle Traditional Chinese character encoding properly throughout pipeline
**AND** MUST use Traditional Chinese prompts and responses in Agno templates

## 5. Interface Specifications

### 5.1 CLI Interface

#### IS-001: Command Structure

```bash
uv run src/main.py classify --input complaints.json [OPTIONS]
```

**Options:**

-   `--input <path>`: Required. Path to JSON file containing complaints
-   `--output <path>`: Optional. Output file path (default: stdout)
-   `--concurrency <int>`: Optional. Max concurrent processing (default: 5)
-   `--confidence-threshold <high|low>`: Optional. Min confidence for auto-acceptance (default: high)
-   `--verbose`: Optional. Enable detailed logging
-   `--dry-run`: Optional. Validate input without processing

#### IS-002: Input JSON Schema

```json
{
	"type": "object",
	"required": ["complaints"],
	"properties": {
		"complaints": {
			"type": "array",
			"items": {
				"type": "object",
				"required": ["id", "content"],
				"properties": {
					"id": { "type": "string", "minLength": 1 },
					"content": { "type": "string", "minLength": 10 },
					"metadata": { "type": "object" }
				}
			}
		}
	}
}
```

#### IS-003: Output JSON Schema

```json
{
	"type": "object",
	"required": ["results", "summary"],
	"properties": {
		"results": {
			"type": "array",
			"items": {
				"type": "object",
				"required": [
					"complaint_id",
					"main_category",
					"sub_category",
					"intents",
					"confidence"
				],
				"properties": {
					"complaint_id": { "type": "string" },
					"main_category": { "type": "string" },
					"sub_category": { "type": "string" },
					"intents": {
						"type": "array",
						"items": {
							"enum": [
								"檢舉告發",
								"請求協助",
								"抱怨問題",
								"請求改善",
								"諮詢問題",
								"不滿服務態度",
								"不滿人員專業度",
								"感謝讚美",
								"提出建議",
								"申請服務",
								"要求賠償",
								"緊急求助",
								"資訊公開",
								"其他"
							]
						}
					},
					"confidence": { "enum": ["high", "low"] },
					"similarity_score": { "type": "number" },
					"reasoning": { "type": "string" },
					"processing_time_ms": { "type": "integer" },
					"rag_candidates": { "type": "array" }
				}
			}
		},
		"summary": {
			"type": "object",
			"properties": {
				"total_processed": { "type": "integer" },
				"high_confidence": { "type": "integer" },
				"low_confidence": { "type": "integer" },
				"errors": { "type": "integer" },
				"average_processing_time_ms": { "type": "number" }
			}
		}
	}
}
```

### 5.2 Agno Agent Interface

#### IS-004: Agent Configuration Schema

```yaml
agent:
    name: "complaint-classifier"
    role: "Taiwan government complaint classification specialist"
    model: "gemini-pro"
    temperature: 0.1
    max_tokens: 1000

templates:
    classification_prompt: "templates/classification.yaml"
    intent_detection_prompt: "templates/intent_detection.yaml"
    confidence_assessment_prompt: "templates/confidence.yaml"

output_schema:
    type: "json"
    schema_file: "schemas/classification_result.json"

retry_config:
    max_retries: 3
    backoff_factor: 2.0
    retry_on: ["timeout", "rate_limit", "malformed_response"]

fallback_config:
    enable: true
    fallback_agent: "simple-classifier"
    fallback_threshold: 3
```

## 6. Dependencies & Integration Requirements

### 6.1 New Dependencies

#### D-001: Agno Framework Integration

**MUST** add to pyproject.toml:

```toml
dependencies = [
    # ... existing dependencies ...
    "agno>=1.0.0",  # AI agent orchestration framework
    "pyyaml>=6.0",  # For Agno configuration files
    "jinja2>=3.1",  # For prompt template rendering
]
```

#### D-002: Enhanced Configuration

**MUST** extend existing Settings class with Agno-specific configuration:

-   Agent configuration parameters
-   Prompt template paths
-   Retry and fallback settings
-   Performance monitoring settings

### 6.2 Integration Points

#### I-001: RAG Infrastructure Integration

**MUST** integrate with existing components:

-   `GeminiEmbedder`: Reuse for query embedding generation
-   `ElasticsearchRepository`: Extend for similarity search operations
-   `Settings`: Extend for Agno configuration management
-   Clean Architecture: New use cases and entities following existing patterns

#### I-002: Agno Framework Integration

**MUST** create new components:

-   `ComplaintClassifierAgent`: Main Agno agent implementation
-   `AgnoLLMInterface`: Agno-to-Gemini Chat API adapter
-   `PromptTemplateManager`: Agno template management
-   `ClassificationUseCase`: Application layer orchestration

## 7. Acceptance Criteria

### 7.1 Functional Acceptance

**AC-001:** RAG + Agno + LLM Pipeline
**GIVEN** a valid complaint text input
**WHEN** the system processes through complete pipeline
**THEN** it returns classification with all required fields populated
**AND** processing completes within 2 seconds
**AND** confidence assessment is accurate based on validation dataset

**AC-002:** Multi-Intent Detection
**GIVEN** complaints with multiple intents
**WHEN** processing through the classification pipeline
**THEN** all relevant intents are detected with >= 85% accuracy
**AND** intent confidence scores correlate with manual validation

**AC-003:** Batch Processing
**GIVEN** JSON file with 20 complaints
**WHEN** processing with concurrency=5
**THEN** all complaints are processed successfully
**AND** results maintain individual processing time requirements
**AND** summary statistics are accurate

### 7.2 Technical Acceptance

**AC-004:** Agno Framework Integration
**GIVEN** Agno agent configuration
**WHEN** initializing the classification system
**THEN** agent loads successfully with all prompt templates
**AND** structured output schema validation works correctly
**AND** retry mechanisms function as configured

**AC-005:** Error Handling & Recovery
**GIVEN** various error conditions (API timeout, malformed input, etc.)
**WHEN** errors occur during processing
**THEN** appropriate error handling strategies are applied
**AND** fallback mechanisms activate when configured
**AND** error information is logged appropriately

## 8. Future Extensibility Considerations

### 8.1 API Interface Preparation

-   CLI interface design should accommodate future REST API endpoints
-   Output format should be compatible with web service responses
-   Error handling patterns should support HTTP status codes

### 8.2 Advanced Features Support

-   Agent configuration should support future intent type additions
-   Prompt templates should be easily modifiable for improved accuracy
-   Performance monitoring should support dashboard integration

### 8.3 Scalability Architecture

-   Concurrent processing limits should be easily adjustable for production
-   Memory management should support larger batch sizes
-   Agno agent should support distributed processing in future versions

This specification provides implementation-ready requirements for senior developers to build the Complaint Classification Agent with full RAG + Agno + LLM integration while maintaining compatibility with existing infrastructure.

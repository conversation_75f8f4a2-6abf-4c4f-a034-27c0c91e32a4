import asyncio
from typing import List, Optional
import google.generativeai as genai
from src.domain.interfaces import EmbedderInterface
from src.config import Settings


class GeminiEmbedder(EmbedderInterface):
    """Gemini嵌入生成器實作"""

    def __init__(self, settings: Settings):
        self.settings = settings
        genai.configure(api_key=settings.gemini_api_key)
        self.model = "gemini-embedding-001"  # 使用gemini-embedding-001模型

    async def generate_embedding(self, text: str) -> List[float]:
        """
        生成單一文本的向量嵌入

        Args:
            text: 要生成嵌入的文本

        Returns:
            向量嵌入（浮點數列表）
        """
        loop = asyncio.get_event_loop()

        def _generate():
            result = genai.embed_content(
                model=self.model,
                content=text,
                task_type=self.settings.embedding_task_type
            )
            return result['embedding']

        embedding = await loop.run_in_executor(None, _generate)
        return embedding

    async def generate_embeddings_batch(self, texts: List[str], batch_size: Optional[int] = None) -> List[List[float]]:
        """
        批次生成多個文本的向量嵌入

        Args:
            texts: 要生成嵌入的文本列表
            batch_size: 批次大小，None表示全部併發處理，1表示逐筆處理

        Returns:
            向量嵌入列表
        """
        if batch_size is None:
            # 原本的行為：全部併發處理
            tasks = [self.generate_embedding(text) for text in texts]
            embeddings = await asyncio.gather(*tasks)
            return embeddings

        # 按批次大小分割文本列表
        all_embeddings = []
        len_texts = len(texts)  # 保存列表長度，避免每次循環都重新計算
        for i in range(0, len_texts, batch_size):
            batch_texts = texts[i:i + batch_size]
            print(f"Processing batch {i // batch_size + 1} of {len_texts // batch_size + 1}")
            # 處理當前批次
            tasks = [self.generate_embedding(text) for text in batch_texts]
            batch_embeddings = await asyncio.gather(*tasks)
            all_embeddings.extend(batch_embeddings)

            # 如果不是最後一個批次，稍微延遲以避免超出速率限制
            if i + batch_size < len_texts:
                await asyncio.sleep(1)  # 1s延遲

        return all_embeddings